using System;
using System.Collections;
using Game.Managers;
using SmartVertex.Tools;
using UnityEngine;

namespace Game.CommandSystem
{
    /// <summary>
    /// Command to move an object to a target position over a duration.
    /// </summary>
    public class MoveCommand : ICoroutineCommand
    {
        /// <summary>Parameters for the move command.</summary>
        public MoveParams Parameters { get; }

        /// <summary>Creates a new MoveCommand.</summary>
        /// <param name="parameters">Move parameters.</param>
        public MoveCommand(MoveParams parameters)
        {
            Parameters = parameters;
        }

        /// <inheritdoc/>
        public IEnumerator Execute()
        {
            string sceneId = ObjectManager.Instance.GetSceneIdForEntity(Parameters.objectId);
            GameObject sceneObject = ObjectManager.Instance.FindScene(sceneId);
            GameObject selectedObject = ObjectManager.Instance.FindById(Parameters.objectId);
            Vector2Int gridSize = ObjectManager.Instance.SceneGridSize;

            Vector3 newPosition = GridUtility.GetWorldPosition
            (sceneObject, gridSize, Parameters.targetPosition, GridUtility.GridPlane.XY);
        }
    }

    /// <summary>Parameters for Move command.</summary>
    [Serializable]
    public struct MoveParams
    {
        public string objectId;
        public Vector2Int targetPosition;
        public float damping;
    }
}
