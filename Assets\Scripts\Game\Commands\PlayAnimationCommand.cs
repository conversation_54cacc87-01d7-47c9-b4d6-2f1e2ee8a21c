using System;
using System.Collections;

namespace Game.CommandSystem
{
    /// <summary>
    /// Command to play an animation on a target object.
    /// </summary>
    public class PlayAnimationCommand : ICoroutineCommand
    {
        /// <summary>Parameters for the play animation command.</summary>
        public PlayAnimationParams Parameters { get; }

        /// <summary>Creates a new PlayAnimationCommand.</summary>
        /// <param name="parameters">Play animation parameters.</param>
        public PlayAnimationCommand(PlayAnimationParams parameters)
        {
            Parameters = parameters;
        }

        /// <inheritdoc/>
        public IEnumerator Execute()
        {
            // TODO: Implement animation playback logic for Parameters.target with Parameters.animationName over Parameters.duration
            yield break;
        }
    }

    /// <summary>Parameters for PlayAnimation command.</summary>
    [Serializable]
    public struct PlayAnimationParams
    {
        public string target;
        public string animationName;
        public float duration;
    }
}
