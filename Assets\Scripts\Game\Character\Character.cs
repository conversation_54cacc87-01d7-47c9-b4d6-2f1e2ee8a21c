using Game.Focus;
using UnityEngine;
using UnityEngine.Events;

namespace Game.CharacterSystem
{
    /// <summary>
    /// Represents a character in the game with animation and dialogue capabilities.
    /// </summary>
    public class Character : Mono<PERSON>ehaviour, IAnimateable
    {
        [SerializeField] private Animator animator;
        [SerializeField] private AudioSource dialogueAudioSource;
        [SerializeField] private string voiceInstructions;
        [SerializeField] private string voiceActor;
        [SerializeField] private float voiceSpeed = 1.0f;

        private string characterId;

        /// <summary>
        /// Animator component for this character.
        /// </summary>
        public Animator Animator => animator;

        /// <summary>
        /// AudioSource used for dialogue playback.
        /// </summary>
        public AudioSource DialogueAudioSource => dialogueAudioSource;

        /// <summary>
        /// voice instructions for this character.
        /// </summary>
        public string VoiceInstructions => voiceInstructions;

        /// <summary>
        /// name of the voice actor for this character.
        /// </summary>
        public string VoiceActor => voiceActor;

        /// <summary>
        /// Speed of the voice playback for this character.
        /// </summary>
        public float VoiceSpeed => voiceSpeed;

        /// <summary>
        /// Unique identifier for this character.
        /// </summary>
        public string CharacterId
        {
            get => characterId;
            set
            {
                if (string.IsNullOrEmpty(value))
                {
                    Debug.LogError("Character ID cannot be null or empty.");
                    return;
                }
                characterId = value;
            }
        }

        public void PlayDialogue(AudioClip audio)
        {
            DialogueAudioSource.clip = audio;
            DialogueAudioSource.Play();
        }

        public void StopDialogue()
        {
            if (DialogueAudioSource.isPlaying)
            {
                DialogueAudioSource.Stop();
            }
        }

        public void PlayAnimation(string animationName, UnityAction onAnimationComplete = null)
        {
            if (animator == null)
            {
                Debug.LogError("Animator component is not assigned.");
                return;
            }
            animator.Play(animationName);
        }

        private void Awake()
        {
            dialogueAudioSource.loop = false;
            dialogueAudioSource.playOnAwake = false;
        }
    }
}