using System.Collections;
using Game.Focus;
using UnityEngine;
using UnityEngine.Events;

namespace Game.CharacterSystem
{
    /// <summary>
    /// Represents a character in the game with animation and dialogue capabilities.
    /// </summary>
    public class Character : Mono<PERSON>ehaviour, IAnimateable
    {
        [SerializeField] private Animator animator;
        [SerializeField] private AudioSource dialogueAudioSource;
        [SerializeField] private string voiceInstructions;
        [SerializeField] private string voiceActor;
        [SerializeField] private float voiceSpeed = 1.0f;

        private string characterId;

        /// <summary>
        /// Animator component for this character.
        /// </summary>
        public Animator Animator => animator;

        /// <summary>
        /// AudioSource used for dialogue playback.
        /// </summary>
        public AudioSource DialogueAudioSource => dialogueAudioSource;

        /// <summary>
        /// voice instructions for this character.
        /// </summary>
        public string VoiceInstructions => voiceInstructions;

        /// <summary>
        /// name of the voice actor for this character.
        /// </summary>
        public string VoiceActor => voiceActor;

        /// <summary>
        /// Speed of the voice playback for this character.
        /// </summary>
        public float VoiceSpeed => voiceSpeed;

        /// <summary>
        /// Unique identifier for this character.
        /// </summary>
        public string CharacterId
        {
            get => characterId;
            set
            {
                if (string.IsNullOrEmpty(value))
                {
                    Debug.LogError("Character ID cannot be null or empty.");
                    return;
                }
                characterId = value;
            }
        }

        public void PlayDialogue(AudioClip audio)
        {
            DialogueAudioSource.clip = audio;
            DialogueAudioSource.Play();
        }

        public void StopDialogue()
        {
            if (DialogueAudioSource.isPlaying)
            {
                DialogueAudioSource.Stop();
            }
        }

        public void PlayAnimation(string animationName, UnityAction onAnimationComplete = null)
        {
            PlayAnimation(animationName, onAnimationComplete, 0);
        }

        public void PlayAnimation(string animationName, UnityAction onAnimationComplete, int layerIndex)
        {
            if (animator == null)
            {
                Debug.LogError("Animator component is not assigned.");
                return;
            }

            animator.Play(animationName, layerIndex);

            if (onAnimationComplete != null)
            {
                StartCoroutine(InvokeAfterAnimationDuration(onAnimationComplete, layerIndex));
            }
        }

        private IEnumerator InvokeAfterAnimationDuration(UnityAction callback, int layerIndex)
        {
            // Wait one frame for the animation to start
            yield return null;

            // Validate layer index
            if (layerIndex >= animator.layerCount)
            {
                Debug.LogError($"Layer index {layerIndex} is out of range. Animator has {animator.layerCount} layers.");
                yield break;
            }

            // Get the current animation state info for the specified layer
            AnimatorStateInfo stateInfo = animator.GetCurrentAnimatorStateInfo(layerIndex);

            // Calculate the actual duration based on clip length and speed
            float animationDuration = stateInfo.length / stateInfo.speed;

            // Wait for the calculated duration
            yield return new WaitForSeconds(animationDuration);

            // Invoke the callback
            callback?.Invoke();
        }

        private void Awake()
        {
            dialogueAudioSource.loop = false;
            dialogueAudioSource.playOnAwake = false;
        }
    }
}