{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 1012, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 1012, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 1012, "tid": 2740, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 1012, "tid": 2740, "ts": 1754551214442752, "dur": 2191, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 1012, "tid": 2740, "ts": 1754551214458787, "dur": 2289, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 1012, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 1012, "tid": 1, "ts": 1754551208929166, "dur": 11661, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 1012, "tid": 1, "ts": 1754551208940833, "dur": 592181, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 1012, "tid": 1, "ts": 1754551209533034, "dur": 623059, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 1012, "tid": 2740, "ts": 1754551214461085, "dur": 25, "ph": "X", "name": "", "args": {}}, {"pid": 1012, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551208924877, "dur": 107661, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209032543, "dur": 5382522, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209035271, "dur": 11089, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209046373, "dur": 6540, "ph": "X", "name": "ProcessMessages 20360", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209052986, "dur": 880, "ph": "X", "name": "ReadAsync 20360", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209053878, "dur": 65, "ph": "X", "name": "ProcessMessages 20483", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209053947, "dur": 169, "ph": "X", "name": "ReadAsync 20483", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209054121, "dur": 4, "ph": "X", "name": "ProcessMessages 869", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209054129, "dur": 56, "ph": "X", "name": "ReadAsync 869", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209054188, "dur": 2, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209054192, "dur": 34, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209054229, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209054233, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209054289, "dur": 2, "ph": "X", "name": "ProcessMessages 274", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209054295, "dur": 40, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209054338, "dur": 2, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209054343, "dur": 41, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209054388, "dur": 2, "ph": "X", "name": "ProcessMessages 296", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209054394, "dur": 64, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209054465, "dur": 1, "ph": "X", "name": "ProcessMessages 264", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209054469, "dur": 51, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209054524, "dur": 2, "ph": "X", "name": "ProcessMessages 299", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209054527, "dur": 46, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209054576, "dur": 1, "ph": "X", "name": "ProcessMessages 185", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209054580, "dur": 144, "ph": "X", "name": "ReadAsync 185", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209054729, "dur": 3, "ph": "X", "name": "ProcessMessages 409", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209054738, "dur": 44, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209054786, "dur": 1, "ph": "X", "name": "ProcessMessages 215", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209054791, "dur": 37, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209054832, "dur": 3, "ph": "X", "name": "ProcessMessages 59", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209054838, "dur": 250, "ph": "X", "name": "ReadAsync 59", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209055095, "dur": 3, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209055101, "dur": 51, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209055156, "dur": 2, "ph": "X", "name": "ProcessMessages 280", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209055161, "dur": 52, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209055217, "dur": 2, "ph": "X", "name": "ProcessMessages 186", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209055223, "dur": 82, "ph": "X", "name": "ReadAsync 186", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209055314, "dur": 3, "ph": "X", "name": "ProcessMessages 329", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209055320, "dur": 80, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209055404, "dur": 3, "ph": "X", "name": "ProcessMessages 476", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209055409, "dur": 47, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209055459, "dur": 2, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209055464, "dur": 83, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209055553, "dur": 2, "ph": "X", "name": "ProcessMessages 250", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209055558, "dur": 128, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209055693, "dur": 2, "ph": "X", "name": "ProcessMessages 270", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209055697, "dur": 53, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209055754, "dur": 4, "ph": "X", "name": "ProcessMessages 377", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209055761, "dur": 45, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209055811, "dur": 2, "ph": "X", "name": "ProcessMessages 197", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209055815, "dur": 288, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209056108, "dur": 2, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209056111, "dur": 176, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209056294, "dur": 4, "ph": "X", "name": "ProcessMessages 987", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209056299, "dur": 196, "ph": "X", "name": "ReadAsync 987", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209056499, "dur": 2, "ph": "X", "name": "ProcessMessages 789", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209056504, "dur": 86, "ph": "X", "name": "ReadAsync 789", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209056594, "dur": 2, "ph": "X", "name": "ProcessMessages 91", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209056601, "dur": 98, "ph": "X", "name": "ReadAsync 91", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209056703, "dur": 2, "ph": "X", "name": "ProcessMessages 462", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209056708, "dur": 91, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209056803, "dur": 2, "ph": "X", "name": "ProcessMessages 420", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209056807, "dur": 88, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209056899, "dur": 2, "ph": "X", "name": "ProcessMessages 242", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209056903, "dur": 76, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209056983, "dur": 2, "ph": "X", "name": "ProcessMessages 69", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209056987, "dur": 87, "ph": "X", "name": "ReadAsync 69", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209057083, "dur": 1, "ph": "X", "name": "ProcessMessages 238", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209057086, "dur": 95, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209057185, "dur": 2, "ph": "X", "name": "ProcessMessages 394", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209057189, "dur": 94, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209057287, "dur": 2, "ph": "X", "name": "ProcessMessages 442", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209057292, "dur": 91, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209057399, "dur": 2, "ph": "X", "name": "ProcessMessages 284", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209057403, "dur": 93, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209057555, "dur": 3, "ph": "X", "name": "ProcessMessages 284", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209057566, "dur": 234, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209057804, "dur": 2, "ph": "X", "name": "ProcessMessages 557", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209057809, "dur": 98, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209057911, "dur": 2, "ph": "X", "name": "ProcessMessages 784", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209057915, "dur": 92, "ph": "X", "name": "ReadAsync 784", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209058012, "dur": 2, "ph": "X", "name": "ProcessMessages 287", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209058015, "dur": 88, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209058107, "dur": 4, "ph": "X", "name": "ProcessMessages 285", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209058113, "dur": 82, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209058200, "dur": 2, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209058203, "dur": 93, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209058309, "dur": 2, "ph": "X", "name": "ProcessMessages 402", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209058313, "dur": 100, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209058417, "dur": 2, "ph": "X", "name": "ProcessMessages 548", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209058421, "dur": 87, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209058512, "dur": 2, "ph": "X", "name": "ProcessMessages 244", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209058515, "dur": 101, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209058620, "dur": 3, "ph": "X", "name": "ProcessMessages 485", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209058625, "dur": 157, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209058787, "dur": 2, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209058791, "dur": 134, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209058929, "dur": 2, "ph": "X", "name": "ProcessMessages 628", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209058934, "dur": 93, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209059079, "dur": 2, "ph": "X", "name": "ProcessMessages 430", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209059083, "dur": 111, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209059200, "dur": 3, "ph": "X", "name": "ProcessMessages 686", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209059205, "dur": 82, "ph": "X", "name": "ReadAsync 686", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209059291, "dur": 2, "ph": "X", "name": "ProcessMessages 289", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209059296, "dur": 134, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209059434, "dur": 3, "ph": "X", "name": "ProcessMessages 308", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209059439, "dur": 92, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209059585, "dur": 8, "ph": "X", "name": "ProcessMessages 511", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209059595, "dur": 118, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209059717, "dur": 3, "ph": "X", "name": "ProcessMessages 608", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209059722, "dur": 394, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209060124, "dur": 3, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209060130, "dur": 108, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209060241, "dur": 3, "ph": "X", "name": "ProcessMessages 843", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209060246, "dur": 50, "ph": "X", "name": "ReadAsync 843", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209060299, "dur": 2, "ph": "X", "name": "ProcessMessages 337", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209060303, "dur": 47, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209060353, "dur": 1, "ph": "X", "name": "ProcessMessages 269", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209060355, "dur": 51, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209060409, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209060412, "dur": 199, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209060616, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209060619, "dur": 81, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209060703, "dur": 2, "ph": "X", "name": "ProcessMessages 710", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209060707, "dur": 164, "ph": "X", "name": "ReadAsync 710", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209060877, "dur": 2, "ph": "X", "name": "ProcessMessages 277", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209060883, "dur": 156, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209061044, "dur": 9, "ph": "X", "name": "ProcessMessages 635", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209061059, "dur": 163, "ph": "X", "name": "ReadAsync 635", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209061227, "dur": 2, "ph": "X", "name": "ProcessMessages 279", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209061236, "dur": 76, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209061316, "dur": 2, "ph": "X", "name": "ProcessMessages 524", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209061323, "dur": 27, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209061352, "dur": 1, "ph": "X", "name": "ProcessMessages 152", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209061356, "dur": 23, "ph": "X", "name": "ReadAsync 152", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209061383, "dur": 18, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209061403, "dur": 1, "ph": "X", "name": "ProcessMessages 85", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209061406, "dur": 15, "ph": "X", "name": "ReadAsync 85", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209061426, "dur": 58, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209061488, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209061530, "dur": 2, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209061534, "dur": 24, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209061564, "dur": 19, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209061588, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209061614, "dur": 20, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209061635, "dur": 1, "ph": "X", "name": "ProcessMessages 59", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209061638, "dur": 18, "ph": "X", "name": "ReadAsync 59", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209061658, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209061661, "dur": 19, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209061683, "dur": 18, "ph": "X", "name": "ReadAsync 186", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209061704, "dur": 1, "ph": "X", "name": "ProcessMessages 83", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209061707, "dur": 128, "ph": "X", "name": "ReadAsync 83", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209061839, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209061845, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209061900, "dur": 2, "ph": "X", "name": "ProcessMessages 557", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209061903, "dur": 27, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209061934, "dur": 1, "ph": "X", "name": "ProcessMessages 65", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209061938, "dur": 70, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209062011, "dur": 1, "ph": "X", "name": "ProcessMessages 231", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209062015, "dur": 43, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209062061, "dur": 2, "ph": "X", "name": "ProcessMessages 291", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209062065, "dur": 40, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209062110, "dur": 1, "ph": "X", "name": "ProcessMessages 171", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209062113, "dur": 77, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209062196, "dur": 1, "ph": "X", "name": "ProcessMessages 168", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209062200, "dur": 57, "ph": "X", "name": "ReadAsync 168", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209062260, "dur": 1, "ph": "X", "name": "ProcessMessages 383", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209062264, "dur": 32, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209062299, "dur": 1, "ph": "X", "name": "ProcessMessages 175", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209062302, "dur": 95, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209062402, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209062405, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209062449, "dur": 1, "ph": "X", "name": "ProcessMessages 280", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209062452, "dur": 30, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209062488, "dur": 168, "ph": "X", "name": "ReadAsync 198", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209062661, "dur": 2, "ph": "X", "name": "ProcessMessages 120", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209062665, "dur": 102, "ph": "X", "name": "ReadAsync 120", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209062772, "dur": 2, "ph": "X", "name": "ProcessMessages 560", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209062779, "dur": 50, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209062831, "dur": 1, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209062834, "dur": 29, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209062867, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209062869, "dur": 66, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209062939, "dur": 1, "ph": "X", "name": "ProcessMessages 207", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209062942, "dur": 47, "ph": "X", "name": "ReadAsync 207", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209062991, "dur": 2, "ph": "X", "name": "ProcessMessages 73", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209062996, "dur": 119, "ph": "X", "name": "ReadAsync 73", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209063119, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209063123, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209063184, "dur": 4, "ph": "X", "name": "ProcessMessages 696", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209063191, "dur": 37, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209063231, "dur": 1, "ph": "X", "name": "ProcessMessages 211", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209063233, "dur": 74, "ph": "X", "name": "ReadAsync 211", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209063311, "dur": 2, "ph": "X", "name": "ProcessMessages 82", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209063315, "dur": 46, "ph": "X", "name": "ReadAsync 82", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209063365, "dur": 3, "ph": "X", "name": "ProcessMessages 557", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209063437, "dur": 58, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209063640, "dur": 7, "ph": "X", "name": "ProcessMessages 595", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209063651, "dur": 75, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209063730, "dur": 19, "ph": "X", "name": "ProcessMessages 845", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209063753, "dur": 110, "ph": "X", "name": "ReadAsync 845", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209063875, "dur": 3, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209063880, "dur": 519, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209064416, "dur": 7, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209064429, "dur": 336, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209064779, "dur": 7, "ph": "X", "name": "ProcessMessages 660", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209064794, "dur": 308, "ph": "X", "name": "ReadAsync 660", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209065108, "dur": 3, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209065113, "dur": 401, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209065570, "dur": 2, "ph": "X", "name": "ProcessMessages 492", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209065573, "dur": 94, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209065671, "dur": 5, "ph": "X", "name": "ProcessMessages 1839", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209065679, "dur": 82, "ph": "X", "name": "ReadAsync 1839", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209065766, "dur": 2, "ph": "X", "name": "ProcessMessages 322", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209065772, "dur": 93, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209065872, "dur": 3, "ph": "X", "name": "ProcessMessages 327", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209065878, "dur": 75, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209065958, "dur": 1, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209065960, "dur": 78, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209066042, "dur": 1, "ph": "X", "name": "ProcessMessages 250", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209066047, "dur": 84, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209066135, "dur": 1, "ph": "X", "name": "ProcessMessages 489", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209066137, "dur": 149, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209066290, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209066293, "dur": 93, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209066394, "dur": 6, "ph": "X", "name": "ProcessMessages 656", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209066403, "dur": 86, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209066495, "dur": 3, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209066501, "dur": 74, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209066581, "dur": 3, "ph": "X", "name": "ProcessMessages 257", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209066587, "dur": 54, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209066646, "dur": 1, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209066649, "dur": 62, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209066716, "dur": 2, "ph": "X", "name": "ProcessMessages 135", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209066720, "dur": 86, "ph": "X", "name": "ReadAsync 135", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209066812, "dur": 3, "ph": "X", "name": "ProcessMessages 169", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209066817, "dur": 64, "ph": "X", "name": "ReadAsync 169", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209066885, "dur": 2, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209066890, "dur": 50, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209066943, "dur": 2, "ph": "X", "name": "ProcessMessages 262", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209066948, "dur": 66, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209067018, "dur": 2, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209067022, "dur": 110, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209067139, "dur": 3, "ph": "X", "name": "ProcessMessages 241", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209067144, "dur": 146, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209067295, "dur": 1, "ph": "X", "name": "ProcessMessages 464", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209067298, "dur": 82, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209067384, "dur": 1, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209067387, "dur": 86, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209067479, "dur": 1, "ph": "X", "name": "ProcessMessages 241", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209067484, "dur": 100, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209067590, "dur": 4, "ph": "X", "name": "ProcessMessages 375", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209067597, "dur": 78, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209067681, "dur": 2, "ph": "X", "name": "ProcessMessages 566", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209067685, "dur": 129, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209067820, "dur": 2, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209067825, "dur": 64, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209067896, "dur": 5, "ph": "X", "name": "ProcessMessages 555", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209067904, "dur": 85, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209068001, "dur": 3, "ph": "X", "name": "ProcessMessages 278", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209068007, "dur": 245, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209068258, "dur": 2, "ph": "X", "name": "ProcessMessages 509", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209068262, "dur": 186, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209068453, "dur": 3, "ph": "X", "name": "ProcessMessages 634", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209068459, "dur": 65, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209068530, "dur": 3, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209068536, "dur": 43, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209068582, "dur": 2, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209068587, "dur": 63, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209068654, "dur": 1, "ph": "X", "name": "ProcessMessages 280", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209068657, "dur": 85, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209068753, "dur": 2, "ph": "X", "name": "ProcessMessages 201", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209068757, "dur": 97, "ph": "X", "name": "ReadAsync 201", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209068860, "dur": 2, "ph": "X", "name": "ProcessMessages 457", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209068865, "dur": 84, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209068953, "dur": 1, "ph": "X", "name": "ProcessMessages 251", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209068956, "dur": 90, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209069050, "dur": 2, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209069055, "dur": 76, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209069136, "dur": 3, "ph": "X", "name": "ProcessMessages 254", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209069142, "dur": 351, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209069499, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209069505, "dur": 92, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209069619, "dur": 3, "ph": "X", "name": "ProcessMessages 626", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209069634, "dur": 82, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209069721, "dur": 2, "ph": "X", "name": "ProcessMessages 447", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209069726, "dur": 82, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209069813, "dur": 2, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209069818, "dur": 66, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209069889, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209069895, "dur": 169, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209070068, "dur": 3, "ph": "X", "name": "ProcessMessages 387", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209070074, "dur": 83, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209070163, "dur": 3, "ph": "X", "name": "ProcessMessages 395", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209070169, "dur": 78, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209070251, "dur": 3, "ph": "X", "name": "ProcessMessages 252", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209070258, "dur": 155, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209070418, "dur": 3, "ph": "X", "name": "ProcessMessages 355", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209070424, "dur": 77, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209070504, "dur": 10, "ph": "X", "name": "ProcessMessages 830", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209070518, "dur": 100, "ph": "X", "name": "ReadAsync 830", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209070623, "dur": 2, "ph": "X", "name": "ProcessMessages 255", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209070626, "dur": 76, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209070706, "dur": 1, "ph": "X", "name": "ProcessMessages 79", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209070708, "dur": 45, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209070759, "dur": 2, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209070763, "dur": 77, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209070844, "dur": 1, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209070847, "dur": 128, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209070981, "dur": 3, "ph": "X", "name": "ProcessMessages 713", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209070987, "dur": 85, "ph": "X", "name": "ReadAsync 713", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209071079, "dur": 3, "ph": "X", "name": "ProcessMessages 163", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209071084, "dur": 58, "ph": "X", "name": "ReadAsync 163", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209071146, "dur": 2, "ph": "X", "name": "ProcessMessages 324", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209071151, "dur": 85, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209071241, "dur": 3, "ph": "X", "name": "ProcessMessages 261", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209071247, "dur": 63, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209071314, "dur": 1, "ph": "X", "name": "ProcessMessages 255", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209071318, "dur": 76, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209071398, "dur": 1, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209071402, "dur": 89, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209071498, "dur": 3, "ph": "X", "name": "ProcessMessages 492", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209071504, "dur": 135, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209071645, "dur": 3, "ph": "X", "name": "ProcessMessages 395", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209071650, "dur": 70, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209071724, "dur": 4, "ph": "X", "name": "ProcessMessages 563", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209071731, "dur": 74, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209071811, "dur": 2, "ph": "X", "name": "ProcessMessages 264", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209071815, "dur": 63, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209071884, "dur": 2, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209071888, "dur": 54, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209071945, "dur": 1, "ph": "X", "name": "ProcessMessages 274", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209071948, "dur": 54, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209072006, "dur": 3, "ph": "X", "name": "ProcessMessages 410", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209072013, "dur": 66, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209072085, "dur": 2, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209072090, "dur": 93, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209072187, "dur": 2, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209072192, "dur": 936, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209073134, "dur": 2, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209073138, "dur": 391, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209073534, "dur": 13, "ph": "X", "name": "ProcessMessages 6108", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209073549, "dur": 94, "ph": "X", "name": "ReadAsync 6108", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209073647, "dur": 2, "ph": "X", "name": "ProcessMessages 371", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209073652, "dur": 92, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209073748, "dur": 2, "ph": "X", "name": "ProcessMessages 344", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209073752, "dur": 93, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209073849, "dur": 2, "ph": "X", "name": "ProcessMessages 485", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209073853, "dur": 72, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209073929, "dur": 3, "ph": "X", "name": "ProcessMessages 86", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209073935, "dur": 87, "ph": "X", "name": "ReadAsync 86", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209074026, "dur": 1, "ph": "X", "name": "ProcessMessages 284", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209074029, "dur": 91, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209074124, "dur": 2, "ph": "X", "name": "ProcessMessages 362", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209074128, "dur": 78, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209074211, "dur": 2, "ph": "X", "name": "ProcessMessages 328", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209074215, "dur": 107, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209074327, "dur": 1, "ph": "X", "name": "ProcessMessages 200", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209074330, "dur": 93, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209074427, "dur": 2, "ph": "X", "name": "ProcessMessages 464", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209074431, "dur": 138, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209074575, "dur": 3, "ph": "X", "name": "ProcessMessages 381", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209074581, "dur": 74, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209074659, "dur": 2, "ph": "X", "name": "ProcessMessages 74", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209074662, "dur": 86, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209074753, "dur": 1, "ph": "X", "name": "ProcessMessages 280", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209074756, "dur": 93, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209074854, "dur": 3, "ph": "X", "name": "ProcessMessages 394", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209074859, "dur": 89, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209074952, "dur": 2, "ph": "X", "name": "ProcessMessages 483", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209074958, "dur": 92, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209075054, "dur": 2, "ph": "X", "name": "ProcessMessages 326", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209075058, "dur": 93, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209075155, "dur": 2, "ph": "X", "name": "ProcessMessages 498", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209075159, "dur": 141, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209075305, "dur": 2, "ph": "X", "name": "ProcessMessages 374", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209075309, "dur": 91, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209075420, "dur": 2, "ph": "X", "name": "ProcessMessages 643", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209075424, "dur": 86, "ph": "X", "name": "ReadAsync 643", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209075513, "dur": 1, "ph": "X", "name": "ProcessMessages 218", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209075516, "dur": 221, "ph": "X", "name": "ReadAsync 218", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209075746, "dur": 2, "ph": "X", "name": "ProcessMessages 335", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209075750, "dur": 95, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209075850, "dur": 3, "ph": "X", "name": "ProcessMessages 575", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209075855, "dur": 83, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209075942, "dur": 1, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209075946, "dur": 94, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209076044, "dur": 2, "ph": "X", "name": "ProcessMessages 458", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209076048, "dur": 91, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209076144, "dur": 2, "ph": "X", "name": "ProcessMessages 554", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209076147, "dur": 86, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209076238, "dur": 2, "ph": "X", "name": "ProcessMessages 386", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209076242, "dur": 92, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209076338, "dur": 2, "ph": "X", "name": "ProcessMessages 625", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209076342, "dur": 77, "ph": "X", "name": "ReadAsync 625", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209076424, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209076427, "dur": 1204, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209077697, "dur": 5, "ph": "X", "name": "ProcessMessages 459", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209077705, "dur": 217, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209077930, "dur": 10, "ph": "X", "name": "ProcessMessages 4327", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209077942, "dur": 76, "ph": "X", "name": "ReadAsync 4327", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209078022, "dur": 2, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209078027, "dur": 257, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209078292, "dur": 3, "ph": "X", "name": "ProcessMessages 402", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209078298, "dur": 310, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209078615, "dur": 9, "ph": "X", "name": "ProcessMessages 1669", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209078627, "dur": 85, "ph": "X", "name": "ReadAsync 1669", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209078731, "dur": 7, "ph": "X", "name": "ProcessMessages 471", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209078744, "dur": 203, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209078964, "dur": 8, "ph": "X", "name": "ProcessMessages 567", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209078978, "dur": 120, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209079348, "dur": 3, "ph": "X", "name": "ProcessMessages 432", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209079354, "dur": 116, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209079474, "dur": 4, "ph": "X", "name": "ProcessMessages 653", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209079481, "dur": 74, "ph": "X", "name": "ReadAsync 653", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209079560, "dur": 1, "ph": "X", "name": "ProcessMessages 139", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209079564, "dur": 79, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209079647, "dur": 2, "ph": "X", "name": "ProcessMessages 219", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209079653, "dur": 86, "ph": "X", "name": "ReadAsync 219", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209079746, "dur": 1, "ph": "X", "name": "ProcessMessages 147", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209079751, "dur": 186, "ph": "X", "name": "ReadAsync 147", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209079943, "dur": 2, "ph": "X", "name": "ProcessMessages 533", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209079948, "dur": 86, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209080041, "dur": 3, "ph": "X", "name": "ProcessMessages 763", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209080047, "dur": 58, "ph": "X", "name": "ReadAsync 763", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209080108, "dur": 1, "ph": "X", "name": "ProcessMessages 307", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209080112, "dur": 52, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209080167, "dur": 2, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209080175, "dur": 95, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209080275, "dur": 2, "ph": "X", "name": "ProcessMessages 275", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209080279, "dur": 85, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209080378, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209080381, "dur": 95, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209080479, "dur": 2, "ph": "X", "name": "ProcessMessages 640", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209080484, "dur": 94, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209080596, "dur": 2, "ph": "X", "name": "ProcessMessages 527", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209080600, "dur": 100, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209080705, "dur": 2, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209080709, "dur": 76, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209080790, "dur": 2, "ph": "X", "name": "ProcessMessages 167", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209080793, "dur": 80, "ph": "X", "name": "ReadAsync 167", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209080878, "dur": 2, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209080882, "dur": 85, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209080974, "dur": 1, "ph": "X", "name": "ProcessMessages 269", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209080977, "dur": 135, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209081116, "dur": 2, "ph": "X", "name": "ProcessMessages 411", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209081124, "dur": 96, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209081226, "dur": 2, "ph": "X", "name": "ProcessMessages 473", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209081230, "dur": 96, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209081335, "dur": 2, "ph": "X", "name": "ProcessMessages 445", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209081351, "dur": 94, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209081449, "dur": 2, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209081452, "dur": 77, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209081535, "dur": 2, "ph": "X", "name": "ProcessMessages 91", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209081538, "dur": 126, "ph": "X", "name": "ReadAsync 91", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209081668, "dur": 1, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209081672, "dur": 93, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209081769, "dur": 2, "ph": "X", "name": "ProcessMessages 741", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209081773, "dur": 80, "ph": "X", "name": "ReadAsync 741", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209081857, "dur": 2, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209082196, "dur": 229, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209082430, "dur": 6, "ph": "X", "name": "ProcessMessages 2804", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209082438, "dur": 100, "ph": "X", "name": "ReadAsync 2804", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209082542, "dur": 2, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209082546, "dur": 81, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209082630, "dur": 1, "ph": "X", "name": "ProcessMessages 245", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209082634, "dur": 87, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209082726, "dur": 2, "ph": "X", "name": "ProcessMessages 371", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209082729, "dur": 141, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209082876, "dur": 3, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209082883, "dur": 212, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209083100, "dur": 18, "ph": "X", "name": "ProcessMessages 364", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209083121, "dur": 78, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209083204, "dur": 4, "ph": "X", "name": "ProcessMessages 759", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209083211, "dur": 89, "ph": "X", "name": "ReadAsync 759", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209083306, "dur": 1, "ph": "X", "name": "ProcessMessages 260", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209083308, "dur": 76, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209083392, "dur": 3, "ph": "X", "name": "ProcessMessages 385", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209083400, "dur": 127, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209083533, "dur": 2, "ph": "X", "name": "ProcessMessages 340", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209083540, "dur": 64, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209083608, "dur": 2, "ph": "X", "name": "ProcessMessages 33", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209083618, "dur": 145, "ph": "X", "name": "ReadAsync 33", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209083767, "dur": 3, "ph": "X", "name": "ProcessMessages 827", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209083772, "dur": 77, "ph": "X", "name": "ReadAsync 827", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209083853, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209083856, "dur": 145, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209084007, "dur": 2, "ph": "X", "name": "ProcessMessages 537", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209084012, "dur": 199, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209084218, "dur": 4, "ph": "X", "name": "ProcessMessages 801", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209084225, "dur": 56, "ph": "X", "name": "ReadAsync 801", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209084286, "dur": 3, "ph": "X", "name": "ProcessMessages 423", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209084291, "dur": 38, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209084332, "dur": 2, "ph": "X", "name": "ProcessMessages 170", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209084337, "dur": 42, "ph": "X", "name": "ReadAsync 170", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209084382, "dur": 1, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209084386, "dur": 165, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209084554, "dur": 2, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209084559, "dur": 96, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209084659, "dur": 3, "ph": "X", "name": "ProcessMessages 510", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209084665, "dur": 75, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209084743, "dur": 2, "ph": "X", "name": "ProcessMessages 456", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209084747, "dur": 49, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209084798, "dur": 1, "ph": "X", "name": "ProcessMessages 213", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209084800, "dur": 154, "ph": "X", "name": "ReadAsync 213", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209085059, "dur": 2, "ph": "X", "name": "ProcessMessages 173", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209085065, "dur": 85, "ph": "X", "name": "ReadAsync 173", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209085154, "dur": 2, "ph": "X", "name": "ProcessMessages 741", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209085158, "dur": 61, "ph": "X", "name": "ReadAsync 741", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209085222, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209085226, "dur": 77, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209085307, "dur": 1, "ph": "X", "name": "ProcessMessages 326", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209085310, "dur": 72, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209085384, "dur": 2, "ph": "X", "name": "ProcessMessages 438", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209085388, "dur": 72, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209085464, "dur": 2, "ph": "X", "name": "ProcessMessages 154", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209085468, "dur": 130, "ph": "X", "name": "ReadAsync 154", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209085609, "dur": 2, "ph": "X", "name": "ProcessMessages 202", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209085614, "dur": 45, "ph": "X", "name": "ReadAsync 202", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209085665, "dur": 3, "ph": "X", "name": "ProcessMessages 633", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209085670, "dur": 246, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209085970, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209085976, "dur": 90, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209086070, "dur": 4, "ph": "X", "name": "ProcessMessages 1660", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209086077, "dur": 72, "ph": "X", "name": "ReadAsync 1660", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209086154, "dur": 1, "ph": "X", "name": "ProcessMessages 213", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209086156, "dur": 81, "ph": "X", "name": "ReadAsync 213", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209086241, "dur": 2, "ph": "X", "name": "ProcessMessages 507", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209086244, "dur": 71, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209086319, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209086322, "dur": 73, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209086399, "dur": 1, "ph": "X", "name": "ProcessMessages 130", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209086401, "dur": 80, "ph": "X", "name": "ReadAsync 130", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209086485, "dur": 2, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209086488, "dur": 75, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209086567, "dur": 1, "ph": "X", "name": "ProcessMessages 345", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209086570, "dur": 81, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209086657, "dur": 2, "ph": "X", "name": "ProcessMessages 257", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209086663, "dur": 53, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209086719, "dur": 2, "ph": "X", "name": "ProcessMessages 276", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209086723, "dur": 50, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209086776, "dur": 2, "ph": "X", "name": "ProcessMessages 280", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209086780, "dur": 77, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209086864, "dur": 2, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209086868, "dur": 69, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209086942, "dur": 2, "ph": "X", "name": "ProcessMessages 129", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209086947, "dur": 96, "ph": "X", "name": "ReadAsync 129", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209087046, "dur": 1, "ph": "X", "name": "ProcessMessages 415", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209087049, "dur": 145, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209087199, "dur": 2, "ph": "X", "name": "ProcessMessages 188", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209087203, "dur": 85, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209087291, "dur": 10, "ph": "X", "name": "ProcessMessages 886", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209087304, "dur": 125, "ph": "X", "name": "ReadAsync 886", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209087433, "dur": 2, "ph": "X", "name": "ProcessMessages 102", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209087437, "dur": 96, "ph": "X", "name": "ReadAsync 102", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209087539, "dur": 2, "ph": "X", "name": "ProcessMessages 277", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209087543, "dur": 92, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209087639, "dur": 2, "ph": "X", "name": "ProcessMessages 441", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209087643, "dur": 133, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209087781, "dur": 2, "ph": "X", "name": "ProcessMessages 424", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209087785, "dur": 80, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209087870, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209087873, "dur": 94, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209087971, "dur": 2, "ph": "X", "name": "ProcessMessages 461", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209087977, "dur": 90, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209088070, "dur": 2, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209088074, "dur": 80, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209088159, "dur": 2, "ph": "X", "name": "ProcessMessages 243", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209088162, "dur": 98, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209088265, "dur": 2, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209088268, "dur": 86, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209088359, "dur": 2, "ph": "X", "name": "ProcessMessages 338", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209088363, "dur": 89, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209088456, "dur": 2, "ph": "X", "name": "ProcessMessages 375", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209088459, "dur": 89, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209088558, "dur": 2, "ph": "X", "name": "ProcessMessages 328", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209088562, "dur": 91, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209088657, "dur": 2, "ph": "X", "name": "ProcessMessages 362", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209088662, "dur": 171, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209088837, "dur": 2, "ph": "X", "name": "ProcessMessages 230", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209088841, "dur": 140, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209088985, "dur": 1, "ph": "X", "name": "ProcessMessages 613", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209088988, "dur": 77, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209089069, "dur": 2, "ph": "X", "name": "ProcessMessages 335", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209089072, "dur": 78, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209089154, "dur": 1, "ph": "X", "name": "ProcessMessages 270", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209089157, "dur": 80, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209089241, "dur": 1, "ph": "X", "name": "ProcessMessages 384", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209089295, "dur": 67, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209089367, "dur": 2, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209089370, "dur": 89, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209089510, "dur": 2, "ph": "X", "name": "ProcessMessages 570", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209089513, "dur": 177, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209089700, "dur": 3, "ph": "X", "name": "ProcessMessages 1153", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209089708, "dur": 88, "ph": "X", "name": "ReadAsync 1153", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209089801, "dur": 19, "ph": "X", "name": "ProcessMessages 443", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209089823, "dur": 76, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209089903, "dur": 1, "ph": "X", "name": "ProcessMessages 207", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209089906, "dur": 89, "ph": "X", "name": "ReadAsync 207", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209089999, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209090002, "dur": 83, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209090088, "dur": 2, "ph": "X", "name": "ProcessMessages 656", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209090091, "dur": 78, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209090173, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209090176, "dur": 96, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209090276, "dur": 1, "ph": "X", "name": "ProcessMessages 335", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209090279, "dur": 90, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209090390, "dur": 2, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209090395, "dur": 106, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209090558, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209090563, "dur": 98, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209090665, "dur": 2, "ph": "X", "name": "ProcessMessages 391", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209090669, "dur": 100, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209090773, "dur": 2, "ph": "X", "name": "ProcessMessages 481", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209090777, "dur": 91, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209090872, "dur": 2, "ph": "X", "name": "ProcessMessages 369", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209090877, "dur": 92, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209090978, "dur": 2, "ph": "X", "name": "ProcessMessages 381", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209090982, "dur": 78, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209091064, "dur": 2, "ph": "X", "name": "ProcessMessages 291", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209091068, "dur": 81, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209091154, "dur": 1, "ph": "X", "name": "ProcessMessages 275", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209091156, "dur": 93, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209091253, "dur": 2, "ph": "X", "name": "ProcessMessages 369", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209091257, "dur": 83, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209091344, "dur": 2, "ph": "X", "name": "ProcessMessages 438", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209091348, "dur": 146, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209091499, "dur": 2, "ph": "X", "name": "ProcessMessages 431", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209091503, "dur": 86, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209091593, "dur": 2, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209091596, "dur": 93, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209091694, "dur": 2, "ph": "X", "name": "ProcessMessages 422", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209091697, "dur": 95, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209091796, "dur": 2, "ph": "X", "name": "ProcessMessages 86", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209091800, "dur": 88, "ph": "X", "name": "ReadAsync 86", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209091891, "dur": 2, "ph": "X", "name": "ProcessMessages 481", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209091895, "dur": 92, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209091991, "dur": 2, "ph": "X", "name": "ProcessMessages 352", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209091995, "dur": 89, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209092147, "dur": 2, "ph": "X", "name": "ProcessMessages 316", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209092151, "dur": 98, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209092255, "dur": 3, "ph": "X", "name": "ProcessMessages 803", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209092260, "dur": 73, "ph": "X", "name": "ReadAsync 803", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209092337, "dur": 2, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209092341, "dur": 83, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209092428, "dur": 2, "ph": "X", "name": "ProcessMessages 294", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209092432, "dur": 136, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209092572, "dur": 2, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209092576, "dur": 79, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209092659, "dur": 2, "ph": "X", "name": "ProcessMessages 79", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209092662, "dur": 92, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209092758, "dur": 2, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209092762, "dur": 88, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209092856, "dur": 2, "ph": "X", "name": "ProcessMessages 327", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209092860, "dur": 86, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209092950, "dur": 2, "ph": "X", "name": "ProcessMessages 283", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209092954, "dur": 88, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209093046, "dur": 2, "ph": "X", "name": "ProcessMessages 280", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209093050, "dur": 90, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209093144, "dur": 2, "ph": "X", "name": "ProcessMessages 363", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209093147, "dur": 79, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209093231, "dur": 2, "ph": "X", "name": "ProcessMessages 236", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209093234, "dur": 535, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209093776, "dur": 4, "ph": "X", "name": "ProcessMessages 599", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209093783, "dur": 59, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209093845, "dur": 2, "ph": "X", "name": "ProcessMessages 497", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209093851, "dur": 80, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209093936, "dur": 3, "ph": "X", "name": "ProcessMessages 95", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209093942, "dur": 55, "ph": "X", "name": "ReadAsync 95", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209094000, "dur": 2, "ph": "X", "name": "ProcessMessages 449", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209094004, "dur": 41, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209094049, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209094051, "dur": 66, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209094119, "dur": 1, "ph": "X", "name": "ProcessMessages 162", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209094122, "dur": 57, "ph": "X", "name": "ReadAsync 162", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209094183, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209094186, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209094241, "dur": 2, "ph": "X", "name": "ProcessMessages 385", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209094247, "dur": 33, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209094283, "dur": 2, "ph": "X", "name": "ProcessMessages 72", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209094288, "dur": 81, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209094375, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209094378, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209094436, "dur": 3, "ph": "X", "name": "ProcessMessages 455", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209094442, "dur": 36, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209094482, "dur": 2, "ph": "X", "name": "ProcessMessages 196", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209094486, "dur": 47, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209094538, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209094541, "dur": 38, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209094582, "dur": 2, "ph": "X", "name": "ProcessMessages 182", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209094587, "dur": 28, "ph": "X", "name": "ReadAsync 182", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209094618, "dur": 2, "ph": "X", "name": "ProcessMessages 159", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209094623, "dur": 35, "ph": "X", "name": "ReadAsync 159", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209094660, "dur": 1, "ph": "X", "name": "ProcessMessages 231", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209094664, "dur": 33, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209094700, "dur": 2, "ph": "X", "name": "ProcessMessages 172", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209094704, "dur": 31, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209094737, "dur": 1, "ph": "X", "name": "ProcessMessages 172", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209094740, "dur": 144, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209094889, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209094893, "dur": 94, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209094993, "dur": 2, "ph": "X", "name": "ProcessMessages 445", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209094997, "dur": 82, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209095084, "dur": 2, "ph": "X", "name": "ProcessMessages 333", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209095088, "dur": 44, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209095135, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209095137, "dur": 82, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209095223, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209095226, "dur": 87, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209095326, "dur": 7, "ph": "X", "name": "ProcessMessages 283", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209095340, "dur": 485, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209095831, "dur": 3, "ph": "X", "name": "ProcessMessages 657", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209095838, "dur": 102, "ph": "X", "name": "ReadAsync 657", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209095944, "dur": 2, "ph": "X", "name": "ProcessMessages 277", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209095947, "dur": 121, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209096073, "dur": 1, "ph": "X", "name": "ProcessMessages 118", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209096076, "dur": 93, "ph": "X", "name": "ReadAsync 118", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209096173, "dur": 2, "ph": "X", "name": "ProcessMessages 453", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209096176, "dur": 93, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209096274, "dur": 2, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209096277, "dur": 93, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209096374, "dur": 2, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209096378, "dur": 88, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209096472, "dur": 2, "ph": "X", "name": "ProcessMessages 280", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209096476, "dur": 74, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209096557, "dur": 4, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209096563, "dur": 247, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209096817, "dur": 3, "ph": "X", "name": "ProcessMessages 289", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209096821, "dur": 102, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209096928, "dur": 3, "ph": "X", "name": "ProcessMessages 477", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209096933, "dur": 117, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209097057, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209097061, "dur": 86, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209097153, "dur": 4, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209097160, "dur": 61, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209097224, "dur": 2, "ph": "X", "name": "ProcessMessages 345", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209097229, "dur": 78, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209097312, "dur": 2, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209097317, "dur": 251, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209097574, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209097579, "dur": 77, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209097682, "dur": 4, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209097691, "dur": 142, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209097838, "dur": 2, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209097843, "dur": 81, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209097929, "dur": 2, "ph": "X", "name": "ProcessMessages 200", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209097933, "dur": 204, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209098143, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209098147, "dur": 93, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209098244, "dur": 2, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209098248, "dur": 86, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209098339, "dur": 2, "ph": "X", "name": "ProcessMessages 393", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209098344, "dur": 113, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209098462, "dur": 2, "ph": "X", "name": "ProcessMessages 197", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209098467, "dur": 187, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209098660, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209098665, "dur": 80, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209098750, "dur": 3, "ph": "X", "name": "ProcessMessages 398", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209098758, "dur": 132, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209098894, "dur": 1, "ph": "X", "name": "ProcessMessages 287", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209098897, "dur": 79, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209098980, "dur": 1, "ph": "X", "name": "ProcessMessages 309", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209098983, "dur": 72, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209099059, "dur": 1, "ph": "X", "name": "ProcessMessages 56", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209099062, "dur": 229, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209099295, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209099298, "dur": 89, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209099391, "dur": 2, "ph": "X", "name": "ProcessMessages 507", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209099394, "dur": 121, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209099522, "dur": 2, "ph": "X", "name": "ProcessMessages 485", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209099527, "dur": 41, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209099571, "dur": 2, "ph": "X", "name": "ProcessMessages 67", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209099574, "dur": 220, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209099847, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209099850, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209099908, "dur": 2, "ph": "X", "name": "ProcessMessages 430", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209099914, "dur": 27, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209099944, "dur": 1, "ph": "X", "name": "ProcessMessages 110", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209099946, "dur": 66, "ph": "X", "name": "ReadAsync 110", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209100016, "dur": 2, "ph": "X", "name": "ProcessMessages 193", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209100084, "dur": 57, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209100146, "dur": 2, "ph": "X", "name": "ProcessMessages 257", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209100151, "dur": 246, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209100403, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209100407, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209100464, "dur": 2, "ph": "X", "name": "ProcessMessages 358", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209100471, "dur": 65, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209100540, "dur": 2, "ph": "X", "name": "ProcessMessages 54", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209100546, "dur": 86, "ph": "X", "name": "ReadAsync 54", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209100637, "dur": 1, "ph": "X", "name": "ProcessMessages 436", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209100640, "dur": 77, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209100720, "dur": 1, "ph": "X", "name": "ProcessMessages 146", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209100723, "dur": 175, "ph": "X", "name": "ReadAsync 146", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209100902, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209100904, "dur": 193, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209101101, "dur": 1, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209101104, "dur": 82, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209101190, "dur": 2, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209101193, "dur": 78, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209101275, "dur": 1, "ph": "X", "name": "ProcessMessages 248", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209101278, "dur": 183, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209101465, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209101467, "dur": 86, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209101557, "dur": 1, "ph": "X", "name": "ProcessMessages 423", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209101559, "dur": 134, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209101698, "dur": 2, "ph": "X", "name": "ProcessMessages 331", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209101702, "dur": 128, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209101835, "dur": 2, "ph": "X", "name": "ProcessMessages 302", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209101839, "dur": 127, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209101972, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209101977, "dur": 78, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209102059, "dur": 2, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209102064, "dur": 89, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209102158, "dur": 2, "ph": "X", "name": "ProcessMessages 384", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209102162, "dur": 87, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209102254, "dur": 4, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209102261, "dur": 197, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209102465, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209102469, "dur": 214, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209102689, "dur": 3, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209102695, "dur": 76, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209102776, "dur": 2, "ph": "X", "name": "ProcessMessages 603", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209102782, "dur": 112, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209102900, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209102905, "dur": 143, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209103051, "dur": 1, "ph": "X", "name": "ProcessMessages 409", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209103054, "dur": 84, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209103144, "dur": 3, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209103149, "dur": 133, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209103290, "dur": 2, "ph": "X", "name": "ProcessMessages 309", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209103295, "dur": 74, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209103374, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209103377, "dur": 123, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209103504, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209103507, "dur": 78, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209103589, "dur": 1, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209103592, "dur": 63, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209103660, "dur": 1, "ph": "X", "name": "ProcessMessages 409", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209103663, "dur": 80, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209103746, "dur": 1, "ph": "X", "name": "ProcessMessages 284", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209103749, "dur": 71, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209103824, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209103827, "dur": 184, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209104015, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209104017, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209104071, "dur": 2, "ph": "X", "name": "ProcessMessages 425", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209104076, "dur": 80, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209104160, "dur": 1, "ph": "X", "name": "ProcessMessages 83", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209104162, "dur": 78, "ph": "X", "name": "ReadAsync 83", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209104246, "dur": 1, "ph": "X", "name": "ProcessMessages 410", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209104249, "dur": 84, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209104337, "dur": 1, "ph": "X", "name": "ProcessMessages 665", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209104339, "dur": 135, "ph": "X", "name": "ReadAsync 665", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209104479, "dur": 2, "ph": "X", "name": "ProcessMessages 413", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209104484, "dur": 200, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209104691, "dur": 10, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209104704, "dur": 82, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209104790, "dur": 2, "ph": "X", "name": "ProcessMessages 515", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209104793, "dur": 78, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209104875, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209104878, "dur": 179, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209105061, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209105063, "dur": 83, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209105150, "dur": 1, "ph": "X", "name": "ProcessMessages 514", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209105153, "dur": 80, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209105237, "dur": 1, "ph": "X", "name": "ProcessMessages 529", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209105239, "dur": 86, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209105380, "dur": 15, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209105397, "dur": 97, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209105499, "dur": 2, "ph": "X", "name": "ProcessMessages 740", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209105561, "dur": 111, "ph": "X", "name": "ReadAsync 740", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209105679, "dur": 3, "ph": "X", "name": "ProcessMessages 1059", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209105684, "dur": 85, "ph": "X", "name": "ReadAsync 1059", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209105773, "dur": 2, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209105776, "dur": 179, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209105960, "dur": 68, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209106034, "dur": 82, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209106120, "dur": 3, "ph": "X", "name": "ProcessMessages 1135", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209106125, "dur": 83, "ph": "X", "name": "ReadAsync 1135", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209106212, "dur": 1, "ph": "X", "name": "ProcessMessages 279", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209106215, "dur": 126, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209106345, "dur": 2, "ph": "X", "name": "ProcessMessages 483", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209106349, "dur": 88, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209106441, "dur": 2, "ph": "X", "name": "ProcessMessages 618", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209106444, "dur": 82, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209106531, "dur": 1, "ph": "X", "name": "ProcessMessages 179", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209106534, "dur": 81, "ph": "X", "name": "ReadAsync 179", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209106618, "dur": 1, "ph": "X", "name": "ProcessMessages 337", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209106621, "dur": 80, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209106706, "dur": 1, "ph": "X", "name": "ProcessMessages 229", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209106710, "dur": 78, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209106793, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209106797, "dur": 181, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209106984, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209106988, "dur": 81, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209107076, "dur": 2, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209107080, "dur": 57, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209107141, "dur": 4, "ph": "X", "name": "ProcessMessages 441", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209107147, "dur": 66, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209107218, "dur": 1, "ph": "X", "name": "ProcessMessages 187", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209107221, "dur": 80, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209107307, "dur": 2, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209107312, "dur": 141, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209107456, "dur": 2, "ph": "X", "name": "ProcessMessages 888", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209107460, "dur": 80, "ph": "X", "name": "ReadAsync 888", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209107543, "dur": 2, "ph": "X", "name": "ProcessMessages 352", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209107546, "dur": 79, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209107631, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209107634, "dur": 195, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209107832, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209107834, "dur": 74, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209107915, "dur": 1, "ph": "X", "name": "ProcessMessages 455", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209107918, "dur": 52, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209107975, "dur": 1, "ph": "X", "name": "ProcessMessages 373", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209107978, "dur": 64, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209108047, "dur": 4, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209108055, "dur": 94, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209108153, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209108155, "dur": 77, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209108236, "dur": 1, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209108239, "dur": 206, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209108448, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209108451, "dur": 86, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209108543, "dur": 1, "ph": "X", "name": "ProcessMessages 419", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209108545, "dur": 80, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209108630, "dur": 2, "ph": "X", "name": "ProcessMessages 413", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209108633, "dur": 125, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209108762, "dur": 2, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209108766, "dur": 118, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209108890, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209108894, "dur": 121, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209109019, "dur": 2, "ph": "X", "name": "ProcessMessages 391", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209109022, "dur": 89, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209109115, "dur": 2, "ph": "X", "name": "ProcessMessages 188", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209109119, "dur": 89, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209109212, "dur": 2, "ph": "X", "name": "ProcessMessages 444", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209109216, "dur": 72, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209109292, "dur": 3, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209109297, "dur": 171, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209109475, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209109479, "dur": 129, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209109614, "dur": 3, "ph": "X", "name": "ProcessMessages 710", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209109620, "dur": 89, "ph": "X", "name": "ReadAsync 710", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209110018, "dur": 3, "ph": "X", "name": "ProcessMessages 232", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209110026, "dur": 65, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209110095, "dur": 3, "ph": "X", "name": "ProcessMessages 433", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209110100, "dur": 69, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209110172, "dur": 1, "ph": "X", "name": "ProcessMessages 156", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209110176, "dur": 69, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209110249, "dur": 1, "ph": "X", "name": "ProcessMessages 244", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209110252, "dur": 30, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209110285, "dur": 1, "ph": "X", "name": "ProcessMessages 118", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209110288, "dur": 27, "ph": "X", "name": "ReadAsync 118", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209110318, "dur": 1, "ph": "X", "name": "ProcessMessages 103", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209110320, "dur": 128, "ph": "X", "name": "ReadAsync 103", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209110453, "dur": 3, "ph": "X", "name": "ProcessMessages 134", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209110458, "dur": 223, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209110685, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209110688, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209110730, "dur": 1, "ph": "X", "name": "ProcessMessages 360", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209110733, "dur": 18, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209110755, "dur": 19, "ph": "X", "name": "ReadAsync 10", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209110777, "dur": 1, "ph": "X", "name": "ProcessMessages 51", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209110780, "dur": 18, "ph": "X", "name": "ReadAsync 51", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209110804, "dur": 20, "ph": "X", "name": "ReadAsync 118", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209110828, "dur": 18, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209110848, "dur": 1, "ph": "X", "name": "ProcessMessages 65", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209110851, "dur": 71, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209110933, "dur": 1, "ph": "X", "name": "ProcessMessages 173", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209110937, "dur": 24, "ph": "X", "name": "ReadAsync 173", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209110962, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209110965, "dur": 305, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209111274, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209111277, "dur": 66, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209111345, "dur": 1, "ph": "X", "name": "ProcessMessages 350", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209111349, "dur": 36, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209111390, "dur": 1, "ph": "X", "name": "ProcessMessages 47", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209111393, "dur": 41, "ph": "X", "name": "ReadAsync 47", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209111438, "dur": 102, "ph": "X", "name": "ReadAsync 202", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209111545, "dur": 2, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209111549, "dur": 67, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209111619, "dur": 2, "ph": "X", "name": "ProcessMessages 498", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209111623, "dur": 115, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209111743, "dur": 3, "ph": "X", "name": "ProcessMessages 177", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209111752, "dur": 96, "ph": "X", "name": "ReadAsync 177", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209111853, "dur": 2, "ph": "X", "name": "ProcessMessages 415", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209111857, "dur": 160, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209112023, "dur": 3, "ph": "X", "name": "ProcessMessages 248", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209112029, "dur": 253, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209112289, "dur": 3, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209112294, "dur": 48, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209112346, "dur": 2, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209112349, "dur": 3364, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209115723, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209115729, "dur": 350, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209116086, "dur": 21, "ph": "X", "name": "ProcessMessages 9707", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209116109, "dur": 238, "ph": "X", "name": "ReadAsync 9707", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209116355, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209116360, "dur": 82, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209116447, "dur": 2, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209116452, "dur": 46, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209116500, "dur": 1, "ph": "X", "name": "ProcessMessages 123", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209116503, "dur": 78, "ph": "X", "name": "ReadAsync 123", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209116586, "dur": 2, "ph": "X", "name": "ProcessMessages 139", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209116590, "dur": 69, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209116664, "dur": 1, "ph": "X", "name": "ProcessMessages 241", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209116668, "dur": 83, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209116756, "dur": 2, "ph": "X", "name": "ProcessMessages 234", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209116760, "dur": 65, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209116829, "dur": 3, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209116835, "dur": 85, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209116924, "dur": 2, "ph": "X", "name": "ProcessMessages 204", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209116928, "dur": 169, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209117101, "dur": 3, "ph": "X", "name": "ProcessMessages 693", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209117106, "dur": 65, "ph": "X", "name": "ReadAsync 693", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209117175, "dur": 2, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209117179, "dur": 74, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209117258, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209117261, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209117295, "dur": 2, "ph": "X", "name": "ProcessMessages 225", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209117299, "dur": 304, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209117609, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209117612, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209117649, "dur": 2, "ph": "X", "name": "ProcessMessages 230", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209117653, "dur": 42, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209117697, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209117700, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209117731, "dur": 1, "ph": "X", "name": "ProcessMessages 209", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209117734, "dur": 80, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209117821, "dur": 1, "ph": "X", "name": "ProcessMessages 281", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209117824, "dur": 125, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209117954, "dur": 4, "ph": "X", "name": "ProcessMessages 39", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209117961, "dur": 57, "ph": "X", "name": "ReadAsync 39", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209118020, "dur": 3, "ph": "X", "name": "ProcessMessages 258", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209118025, "dur": 4820, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209122855, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209122860, "dur": 566, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209123431, "dur": 34, "ph": "X", "name": "ProcessMessages 14120", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209123467, "dur": 88, "ph": "X", "name": "ReadAsync 14120", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209123558, "dur": 3, "ph": "X", "name": "ProcessMessages 1034", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209123562, "dur": 49, "ph": "X", "name": "ReadAsync 1034", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209123614, "dur": 1, "ph": "X", "name": "ProcessMessages 238", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209123617, "dur": 45, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209123666, "dur": 1, "ph": "X", "name": "ProcessMessages 254", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209123668, "dur": 38, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209123709, "dur": 1, "ph": "X", "name": "ProcessMessages 89", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209123712, "dur": 47, "ph": "X", "name": "ReadAsync 89", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209123764, "dur": 41, "ph": "X", "name": "ReadAsync 219", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209123808, "dur": 1, "ph": "X", "name": "ProcessMessages 179", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209123811, "dur": 39, "ph": "X", "name": "ReadAsync 179", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209123853, "dur": 1, "ph": "X", "name": "ProcessMessages 171", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209123856, "dur": 37, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209123896, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209123899, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209123962, "dur": 38, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209124002, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209124005, "dur": 42, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209124049, "dur": 1, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209124052, "dur": 5773, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209129833, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209129838, "dur": 13319, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209143225, "dur": 146, "ph": "X", "name": "ProcessMessages 14403", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209143485, "dur": 2240, "ph": "X", "name": "ReadAsync 14403", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209145736, "dur": 8, "ph": "X", "name": "ProcessMessages 1312", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209146276, "dur": 85001, "ph": "X", "name": "ReadAsync 1312", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209231353, "dur": 354, "ph": "X", "name": "ProcessMessages 20491", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209231788, "dur": 16145, "ph": "X", "name": "ReadAsync 20491", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209247966, "dur": 102, "ph": "X", "name": "ProcessMessages 20506", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209248077, "dur": 1085, "ph": "X", "name": "ReadAsync 20506", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209249172, "dur": 78, "ph": "X", "name": "ProcessMessages 20483", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209249254, "dur": 38, "ph": "X", "name": "ReadAsync 20483", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209249296, "dur": 2, "ph": "X", "name": "ProcessMessages 67", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209249300, "dur": 415, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209249722, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209249727, "dur": 119, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209249864, "dur": 3, "ph": "X", "name": "ProcessMessages 391", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209249870, "dur": 90, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209249962, "dur": 1, "ph": "X", "name": "ProcessMessages 385", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209249966, "dur": 72765, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209322743, "dur": 4, "ph": "X", "name": "ProcessMessages 302", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209322750, "dur": 838, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209323594, "dur": 787, "ph": "X", "name": "ProcessMessages 17699", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209324391, "dur": 59352, "ph": "X", "name": "ReadAsync 17699", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209383754, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209383761, "dur": 2531, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209388379, "dur": 2541, "ph": "X", "name": "ProcessMessages 920", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209390930, "dur": 29863, "ph": "X", "name": "ReadAsync 920", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209420808, "dur": 4, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209420815, "dur": 103, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209420921, "dur": 8, "ph": "X", "name": "ProcessMessages 512", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209420931, "dur": 872, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209421810, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209421816, "dur": 69, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209421887, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209421890, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209421940, "dur": 337, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209422280, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209422323, "dur": 68, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209422394, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209422397, "dur": 212, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209422613, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209422616, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209422665, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209422667, "dur": 4249, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209426926, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209426932, "dur": 192, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209427149, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209427156, "dur": 1428, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209428594, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209428599, "dur": 76, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209428678, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209428683, "dur": 43, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209428729, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209428732, "dur": 75, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209428810, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209428814, "dur": 86, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209428903, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209428907, "dur": 55, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209428965, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209428967, "dur": 1082, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209430079, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209430085, "dur": 106, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209430196, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209430200, "dur": 58, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209430261, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209430265, "dur": 61, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209430332, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209430335, "dur": 69, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209430409, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209430413, "dur": 68, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209430486, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209430490, "dur": 84, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209430577, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209430580, "dur": 52, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209430635, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209430663, "dur": 50, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209430717, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209430721, "dur": 114, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209430839, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209430844, "dur": 22595, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209453451, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209453457, "dur": 123, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209453585, "dur": 7, "ph": "X", "name": "ProcessMessages 356", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209453594, "dur": 5476, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209459093, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209459098, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209459159, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209459163, "dur": 3107, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209462280, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209462285, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209462350, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209462352, "dur": 282, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209462639, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209462642, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209462685, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209462687, "dur": 1068, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209463762, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209463766, "dur": 76, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209463846, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209463848, "dur": 1164, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209465021, "dur": 6, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209465033, "dur": 80, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209465116, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209465119, "dur": 2012, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209467139, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209467144, "dur": 69, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209467216, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209467218, "dur": 3956, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209471185, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209471190, "dur": 82, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209471276, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209471281, "dur": 8207, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209479497, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209479503, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209479561, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209479565, "dur": 237, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209479808, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209479812, "dur": 77, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209479893, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209479897, "dur": 68, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209479978, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209479990, "dur": 132, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209480126, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209480129, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209480196, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209480199, "dur": 58, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209480261, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209480264, "dur": 629, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209480899, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209480902, "dur": 69, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209480976, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209480980, "dur": 165, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209481150, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209481153, "dur": 10148, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209491333, "dur": 11, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209491347, "dur": 96, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209491447, "dur": 15, "ph": "X", "name": "ProcessMessages 868", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209491463, "dur": 13484, "ph": "X", "name": "ReadAsync 868", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209504958, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209504963, "dur": 14076, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209519047, "dur": 7, "ph": "X", "name": "ProcessMessages 244", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209519057, "dur": 61, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209519122, "dur": 3, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209519141, "dur": 463368, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209982519, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551209982525, "dur": 42264, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210024812, "dur": 23932, "ph": "X", "name": "ProcessMessages 188", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210048754, "dur": 4626, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210053393, "dur": 14, "ph": "X", "name": "ProcessMessages 656", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210053411, "dur": 87, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210053504, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210053509, "dur": 75, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210053587, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210053589, "dur": 66, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210053664, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210053668, "dur": 1274, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210054950, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210054956, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210055004, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210055007, "dur": 2263, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210057280, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210057285, "dur": 112, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210057403, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210057408, "dur": 59, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210057472, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210057475, "dur": 1318, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210058801, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210058805, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210058851, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210058854, "dur": 2435, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210061299, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210061304, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210061353, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210061356, "dur": 1260, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210062623, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210062628, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210062701, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210062705, "dur": 2362, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210065076, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210065082, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210065123, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210065126, "dur": 2633, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210067769, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210067774, "dur": 82, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210067862, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210067866, "dur": 1210, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210069084, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210069088, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210069129, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210069133, "dur": 1079, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210070221, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210070226, "dur": 96, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210070326, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210070330, "dur": 1742, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210072080, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210072086, "dur": 88, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210072178, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210072183, "dur": 561, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210072749, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210072752, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210072824, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210072827, "dur": 2514, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210075351, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210075356, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210075414, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210075416, "dur": 424, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210075846, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210075849, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210075887, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210075889, "dur": 543, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210076440, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210076444, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210076501, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210076505, "dur": 3333, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210079849, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210079854, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210079891, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210079895, "dur": 62, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210079962, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210079984, "dur": 1078, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210081067, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210081071, "dur": 109, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210081184, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210081189, "dur": 2367, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210083564, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210083568, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210083603, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210083606, "dur": 2790, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210086405, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210086410, "dur": 90, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210086503, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210086506, "dur": 1077, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210087587, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210087590, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210087619, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210087622, "dur": 3087, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210090718, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210090724, "dur": 79, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210090807, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210090811, "dur": 600, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210091416, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210091419, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210091462, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210091465, "dur": 1967, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210093442, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210093447, "dur": 101, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210093552, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210093556, "dur": 1320, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210094884, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210094889, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210094959, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210094962, "dur": 61, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210095029, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210095033, "dur": 2631, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210097672, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210097678, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210097724, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210097729, "dur": 1379, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210099117, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210099123, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210099177, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210099181, "dur": 175, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210099361, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210099364, "dur": 80, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210099448, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210099451, "dur": 3326, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210102788, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210102793, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210102836, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210102839, "dur": 1200, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210104046, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210104060, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210104088, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210104091, "dur": 1935, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210106035, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210106039, "dur": 72, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210106115, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210106119, "dur": 867, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210106996, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210107001, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210107051, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210107055, "dur": 2942, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210110008, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210110014, "dur": 72, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210110090, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210110093, "dur": 1470, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210111574, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210111579, "dur": 94, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210111677, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210111680, "dur": 1244, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210112930, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210112933, "dur": 65, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210113002, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210113005, "dur": 1711, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210114722, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210114727, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210114779, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210114782, "dur": 823, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210115613, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210115616, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210115656, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210115659, "dur": 1613, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210117282, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210117287, "dur": 96, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210117391, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210117395, "dur": 86, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210117486, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210117488, "dur": 77, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210117569, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210117572, "dur": 84, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210117661, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210117664, "dur": 94, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210117762, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210117765, "dur": 118, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210117887, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210117889, "dur": 89, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210117983, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210117987, "dur": 70, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210118061, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210118066, "dur": 78, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210118149, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210118152, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210118212, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210118216, "dur": 117, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210118339, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210118344, "dur": 5350, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210123705, "dur": 4, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210123713, "dur": 240, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210123958, "dur": 40, "ph": "X", "name": "ProcessMessages 2736", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551210124001, "dur": 3522301, "ph": "X", "name": "ReadAsync 2736", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551213646314, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551213646320, "dur": 98, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551213646422, "dur": 38, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551213646463, "dur": 250245, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551213896719, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551213896724, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551213896767, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551213896773, "dur": 236454, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551214133240, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551214133246, "dur": 97, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551214133350, "dur": 58, "ph": "X", "name": "ProcessMessages 488", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551214133412, "dur": 19112, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551214152537, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551214152544, "dur": 79, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551214152629, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551214152636, "dur": 4375, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551214157021, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551214157027, "dur": 70, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551214157101, "dur": 36, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551214157139, "dur": 224467, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551214381618, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551214381624, "dur": 101, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551214381734, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551214381740, "dur": 1664, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551214383416, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551214383421, "dur": 70, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551214383494, "dur": 53, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551214383550, "dur": 806, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551214384382, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551214384388, "dur": 94, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551214384488, "dur": 794, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 1012, "tid": 12884901888, "ts": 1754551214385292, "dur": 28672, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 1012, "tid": 2740, "ts": 1754551214461115, "dur": 6725, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 1012, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 1012, "tid": 8589934592, "ts": 1754551208920117, "dur": 1236046, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 1012, "tid": 8589934592, "ts": 1754551210156182, "dur": 15, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 1012, "tid": 8589934592, "ts": 1754551210156200, "dur": 2711, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 1012, "tid": 2740, "ts": 1754551214467845, "dur": 78, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 1012, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 1012, "tid": 4294967296, "ts": 1754551208885107, "dur": 5532649, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 1012, "tid": 4294967296, "ts": 1754551208892652, "dur": 16995, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 1012, "tid": 4294967296, "ts": 1754551214417909, "dur": 16411, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 1012, "tid": 4294967296, "ts": 1754551214428776, "dur": 257, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 1012, "tid": 4294967296, "ts": 1754551214434544, "dur": 36, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 1012, "tid": 2740, "ts": 1754551214467926, "dur": 20, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1754551209026904, "dur": 129, "ph": "X", "name": "IPC_Client_InitializeAndConnectToParent", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754551209027130, "dur": 6400, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754551209033560, "dur": 3437, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754551209037370, "dur": 315, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1754551209037686, "dur": 629, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754551209038636, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_2D35B77B8A4D18E6.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209038703, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_B407E9C8BAAF60F1.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209038959, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_327795C09041C15A.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209040546, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_26FEC58E1C96E48D.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209040979, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_A547AE6165B65363.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209041379, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_1936861B41E20424.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209042151, "dur": 1088, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_30D61BF1868D39BE.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209043771, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_FCD037BA9EE2A890.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209043837, "dur": 101, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_AEF938A65F199009.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209044274, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_A68BFF195C835CF7.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209044469, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_10E454BD63844E1A.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209045137, "dur": 136, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_00EA55EFDF82E80F.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209045904, "dur": 91, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_AE211F434B9B95F3.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209046008, "dur": 143, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Apple.Extensions.Common.dll_C24B01A016BE7033.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209046260, "dur": 86, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_5001ACC648CFBA23.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209046362, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Android.Extensions.dll_4BA974FBC191E139.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209046424, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.dll_05415FEA72405DAF.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209046494, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_10B00844B4F608BF.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209047169, "dur": 7209, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754551209054394, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209054568, "dur": 87, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209054956, "dur": 75, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209055040, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754551209055276, "dur": 106, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209055720, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209055798, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754551209055924, "dur": 75, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754551209056007, "dur": 85, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209056294, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209056874, "dur": 204, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209057097, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754551209057205, "dur": 78, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754551209057291, "dur": 83, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209057393, "dur": 80, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209057562, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.ref.dll_84A4293DBDD182DB.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209057684, "dur": 81, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754551209057773, "dur": 76, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209057861, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754551209058167, "dur": 131, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209058385, "dur": 94, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209058491, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754551209058796, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209058919, "dur": 103, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209059028, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754551209059091, "dur": 76, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209059447, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754551209059523, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209059698, "dur": 87, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209059899, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_4B16FAA5016376E3.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209059983, "dur": 106, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_6717AAFEBC09DAE0.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209060143, "dur": 147, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_EC1F0C20321316E3.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209060343, "dur": 79, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209060517, "dur": 179, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209060705, "dur": 87, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754551209060862, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209061060, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.InputSystem.ref.dll_FD2EC87C14EBE081.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209061138, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209061204, "dur": 78, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754551209061289, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209061366, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754551209061448, "dur": 122, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209061820, "dur": 91, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209061926, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754551209062093, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754551209062350, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.PerformanceTesting.ref.dll_F99DC928B1D72E5E.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209062415, "dur": 82, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209062512, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754551209062587, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209062709, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209062789, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209062931, "dur": 78, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Profiling.Core.ref.dll_DDC12D7300735955.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209063020, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Profiling.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209063200, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Profiling.Core.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209063270, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Profiling.Core.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754551209063346, "dur": 80, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Profiling.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209063443, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Profiling.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209063599, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.ScriptableBuildPipeline.ref.dll_27C8026E25690113.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209063720, "dur": 103, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ScriptableBuildPipeline.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754551209064203, "dur": 114, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754551209064656, "dur": 159, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209064839, "dur": 173, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754551209065028, "dur": 236, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209065294, "dur": 75, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209065607, "dur": 75, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Common.Runtime.ref.dll_77798F371487C3E9.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209065692, "dur": 139, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209065934, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Runtime.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754551209066115, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Tilemap.Extras.ref.dll_371E356D6F5B6577.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209066182, "dur": 124, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209066319, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754551209066480, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754551209066560, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209066623, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209066888, "dur": 83, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ResourceManager.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754551209066979, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ResourceManager.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209067065, "dur": 78, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ResourceManager.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754551209067157, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ResourceManager.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209067415, "dur": 76, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209067713, "dur": 133, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209067963, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Settings.Editor.ref.dll_7F5B154C05A780B0.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209068205, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754551209068285, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209068522, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209068584, "dur": 76, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754551209068672, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209068779, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754551209068855, "dur": 81, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209069069, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Core.ref.dll_D75DF4EA1AFE54F4.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209069151, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209069233, "dur": 82, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754551209069362, "dur": 127, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754551209069552, "dur": 75, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209070105, "dur": 106, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Addressables.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754551209070219, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Addressables.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209070305, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Addressables.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754551209070420, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Addressables.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209070481, "dur": 75, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Addressables.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209070658, "dur": 80, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Common.Editor.ref.dll_BD59A5CBF9D52B22.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209070747, "dur": 85, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209070841, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754551209070997, "dur": 95, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209071102, "dur": 91, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209071458, "dur": 123, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Path.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754551209071682, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Path.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209071825, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.SpriteShape.Runtime.ref.dll_BCD600250821B9C8.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209071907, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209071971, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754551209072104, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Runtime.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754551209072249, "dur": 78, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209072410, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.InternalAPIEditorBridge.001.ref.dll_C275F416F58A86FE.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209072543, "dur": 87, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.001.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754551209073647, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209073719, "dur": 370, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754551209074102, "dur": 84, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209074249, "dur": 75, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209074335, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209074504, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Flow.ref.dll_1E6ED5409D07C9A3.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209074631, "dur": 75, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754551209074803, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209074917, "dur": 82, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209075011, "dur": 113, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209075227, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.Editor.ref.dll_159E061D77A10B86.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209075354, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754551209075436, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209075563, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209075637, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209075880, "dur": 77, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Addressables.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754551209076004, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Addressables.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209076117, "dur": 120, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Addressables.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209076246, "dur": 80, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Addressables.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209076480, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209076542, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754551209076625, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209076814, "dur": 75, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209077015, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209077147, "dur": 240, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754551209077418, "dur": 95, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209077547, "dur": 140, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754551209077696, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209078405, "dur": 118, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Core.Runtime.Shared.ref.dll_547B576A1A56EABF.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209079027, "dur": 164, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Universal.Config.Runtime.ref.dll_5D92000A132D4840.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209079210, "dur": 106, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209079336, "dur": 164, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754551209079526, "dur": 83, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209079632, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754551209079721, "dur": 95, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209080073, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209080291, "dur": 135, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209080543, "dur": 83, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754551209080634, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209080706, "dur": 91, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209080883, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.State.ref.dll_D91762B7076BE462.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209080984, "dur": 78, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754551209081070, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209081193, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209081312, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209081461, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.InternalAPIEngineBridge.001.ref.dll_B4F0ACC15A996E43.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209081526, "dur": 119, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209081720, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.001.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209081805, "dur": 75, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.001.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754551209081939, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209082107, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Universal.Runtime.ref.dll_5400A10AF7CC6BEA.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209082243, "dur": 77, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754551209082633, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209082788, "dur": 207, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209083124, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Searcher.Editor.ref.dll_12B7E1785E41BE0E.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209083237, "dur": 75, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754551209083318, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209083481, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209083662, "dur": 126, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.ShaderGraph.Utilities.ref.dll_0524057423981A9D.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209083796, "dur": 77, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209083883, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754551209083967, "dur": 131, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209084233, "dur": 81, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209084435, "dur": 111, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209084817, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209084906, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209085133, "dur": 87, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209085231, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754551209085309, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Runtime.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209085516, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209085759, "dur": 91, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209085910, "dur": 78, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Common.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209086044, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Common.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754551209086113, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209086544, "dur": 110, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754551209086722, "dur": 78, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209086961, "dur": 76, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209087134, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209087511, "dur": 87, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209087607, "dur": 96, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754551209087795, "dur": 83, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209087887, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209088041, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.PixelPerfect.ref.dll_88C9F7A57CCB704B.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209088124, "dur": 75, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209088210, "dur": 125, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754551209088440, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754551209088575, "dur": 75, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209088761, "dur": 95, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209088866, "dur": 76, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754551209088950, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209089032, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754551209089159, "dur": 92, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209089403, "dur": 139, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209089550, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754551209089743, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209089965, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209090209, "dur": 77, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209090298, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209090685, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754551209090767, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209090839, "dur": 117, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209091150, "dur": 76, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209091237, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754551209091366, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754551209091449, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209091646, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Universal.Editor.ref.dll_0FF7AC68384616B1.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209091714, "dur": 84, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209091939, "dur": 140, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754551209092093, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209092178, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209092376, "dur": 82, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209092469, "dur": 82, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754551209092562, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209092728, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209092947, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209093008, "dur": 124, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754551209093235, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754551209093366, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209093550, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.TestRunner.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754551209093631, "dur": 83, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.TestRunner.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754551209093724, "dur": 171, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.TestRunner.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754551209093903, "dur": 84, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Rider.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754551209094052, "dur": 236, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754551209094351, "dur": 85, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209094509, "dur": 76, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209094766, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualStudio.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754551209094898, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209094970, "dur": 75, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754551209095058, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209095397, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualStudio.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754551209095517, "dur": 78, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209095615, "dur": 97, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209095721, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754551209095800, "dur": 83, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209095897, "dur": 77, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754551209095996, "dur": 89, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209096342, "dur": 80, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209096552, "dur": 98, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209096659, "dur": 76, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754551209096749, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209096880, "dur": 96, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209096984, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209097109, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 0, "ts": 1754551209097167, "dur": 75, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4747638433968585886.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209097663, "dur": 89, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209097760, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12812556936427222528.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209098228, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Common.Path.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209098289, "dur": 135, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12697856024727907050.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209098747, "dur": 87, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.PixelPerfect.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209098842, "dur": 81, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3410860103080533286.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209099177, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.PixelPerfect.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754551209099240, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Sprite.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754551209099328, "dur": 133, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209099470, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3648465134513896125.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209099781, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Sprite.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754551209099886, "dur": 81, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Tilemap.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209099975, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5806762800881712256.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209100329, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Tilemap.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754551209100492, "dur": 80, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209100920, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Burst.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754551209100981, "dur": 76, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754551209101070, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209101138, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17346584914308636752.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209101479, "dur": 167, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InternalAPIEngineBridge.001.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754551209101655, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InternalAPIEngineBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209102042, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mathematics.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754551209102120, "dur": 85, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209102582, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209103069, "dur": 88, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209103419, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PerformanceTesting.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754551209103479, "dur": 126, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PlasticSCM.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754551209103620, "dur": 85, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209103721, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9111662996337956171.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209104089, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Profiling.Core.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754551209104155, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Profiling.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209104230, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3232911574759799904.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209104595, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.ShaderLibrary.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754551209104668, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209104827, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754551209104941, "dur": 122, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754551209105072, "dur": 124, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209105637, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754551209105705, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209105836, "dur": 79, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754551209105924, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209106563, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209106624, "dur": 89, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209106722, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754551209106800, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209106872, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754551209106939, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209107184, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5803490648119114145.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209107613, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209107676, "dur": 93, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209107928, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209108410, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ScriptableBuildPipeline.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754551209108477, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ScriptableBuildPipeline.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209109025, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Searcher.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754551209109468, "dur": 116, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Settings.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754551209109603, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Settings.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209109687, "dur": 76, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1177933628359149393.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209110101, "dur": 99, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209110209, "dur": 90, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9675442845102135732.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209110624, "dur": 107, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754551209110741, "dur": 77, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209111211, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754551209111267, "dur": 77, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754551209111352, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209111418, "dur": 87, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/8899139255040401798.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209111793, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754551209111851, "dur": 82, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/PsdPlugin.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754551209111940, "dur": 77, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PsdPlugin.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209112024, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PsdPlugin.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209112145, "dur": 76, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PsdPlugin.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209112234, "dur": 87, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PsdPlugin.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754551209112331, "dur": 85, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/PsdPlugin.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209112431, "dur": 126, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PsdPlugin.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209112702, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2499735490941455596.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209113095, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/PsdPlugin.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754551209113491, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/RTLTMPro.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209115491, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Burst.CodeGen.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754551209115660, "dur": 98, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209115766, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12248871281822510316.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209116334, "dur": 695, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Burst.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754551209117094, "dur": 135, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209117251, "dur": 78, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209117340, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754551209117418, "dur": 100, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209117526, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754551209117794, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3938377011463375229.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209118133, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.CollabProxy.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754551209118306, "dur": 174, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209118919, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Collections.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754551209118988, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.DocCodeSamples.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754551209119064, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209120006, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209120136, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754551209120918, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209121679, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.TestFramework.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754551209121797, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InternalAPIEditorBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209122194, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InternalAPIEditorBridge.001.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754551209122301, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209122733, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209123183, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10831531723267835799.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209123533, "dur": 403, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754551209123990, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209124051, "dur": 100, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209124159, "dur": 106, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754551209124275, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209124460, "dur": 80, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209124674, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12900333509587573630.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209125105, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ResourceManager.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209125983, "dur": 80, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Splines.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209126493, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209127078, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10571807241835812913.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209127731, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/RTLTMPro-Tests.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209128397, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Common.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209128820, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Common.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754551209128879, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.SpriteShape.Runtime.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754551209128943, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.SpriteShape.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209130418, "dur": 575, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10654825778349625970.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209131962, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6262281476893245489.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209132284, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Collections.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754551209132343, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Runtime.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754551209132404, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209132771, "dur": 97, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Runtime.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754551209133341, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Splines.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209134645, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754551209134746, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/SmartVertex.Tools.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209135990, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17738801749825545598.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209136556, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Addressables.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209139586, "dur": 136, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Addressables.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754551209139777, "dur": 100, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209140218, "dur": 172, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipeline.Universal.ShaderLibrary.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754551209140413, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Runtime.Shared.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754551209140610, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209140676, "dur": 76, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10397561839769426034.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209141149, "dur": 125, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Runtime.Shared.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754551209141329, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209141921, "dur": 113, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Config.Runtime.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754551209142128, "dur": 100, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209142237, "dur": 4066, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15085861467720516389.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209146862, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Config.Runtime.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754551209146982, "dur": 140, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754551209147137, "dur": 105, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209147251, "dur": 68790, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10292501669419677951.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209216712, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209217227, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209218725, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/RTLTMPro-Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754551209218791, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/SmartVertex.EditorTools.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754551209218851, "dur": 88, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/SmartVertex.EditorTools.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209218949, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/SmartVertex.EditorTools.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209219021, "dur": 126, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/SmartVertex.EditorTools.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754551209219322, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/SmartVertex.EditorTools.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754551209220017, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Addressables.DocExampleCode.Editor.Tests.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209220082, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Addressables.DocExampleCode.Editor.Tests.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209220546, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14485061826818414241.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209220967, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209221438, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754551209221561, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209221633, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10010842633742469623.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209222211, "dur": 97, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.SettingsProvider.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754551209222316, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209223875, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754551209223978, "dur": 76, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Animation.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209224661, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209224723, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17296387151066238333.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209225115, "dur": 8816, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Editor.Shared.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754551209234031, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209234616, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209234843, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754551209235517, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Shared.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754551209235624, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Animation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209236320, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.IK.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209237396, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.IK.Runtime.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754551209237519, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209238108, "dur": 77, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Aseprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209238360, "dur": 80, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754551209239043, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Aseprite.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754551209239232, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209239556, "dur": 79, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209240849, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Psdimporter.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754551209240995, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Cinemachine.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209241077, "dur": 91, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6154019870087291391.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209241824, "dur": 133, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Cinemachine.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754551209241994, "dur": 105, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754551209242153, "dur": 163, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209242333, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15183149355271759364.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209242767, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Game.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754551209242876, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Game.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209243257, "dur": 6495, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/7492027457337787470.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754551209250227, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Game.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754551209250347, "dur": 109, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209250466, "dur": 87, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209250561, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754551209251270, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/PPv2URPConverters.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754551209251326, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.PixelPerfect.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754551209251392, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.PixelPerfect.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209252126, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.PixelPerfect.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754551209252183, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Cinemachine.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754551209252251, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Cinemachine.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209252309, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754551209252374, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754551209252482, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754551209038388, "dur": 214674, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754551209253109, "dur": 5131200, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754551214384352, "dur": 62, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754551214384414, "dur": 61, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754551214384478, "dur": 52, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754551214384614, "dur": 163, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754551214384854, "dur": 2945, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1754551209039042, "dur": 214096, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209253205, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209253411, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_2C5395855EB59FDC.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754551209253483, "dur": 306, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209253800, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_1D276C237AA35AED.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754551209253981, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_145E12438C7FA2B9.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754551209254043, "dur": 953, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209255535, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209256573, "dur": 1306, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209258077, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209258656, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209258728, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754551209258833, "dur": 677, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754551209259654, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209259725, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754551209259846, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209260045, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754551209260365, "dur": 1939, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209262367, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209262444, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_4B16FAA5016376E3.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754551209262673, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754551209262735, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209262811, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209262882, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209262942, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209263008, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209263163, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209263436, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209263576, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209263637, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754551209263696, "dur": 4330, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209268041, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209268110, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754551209268264, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209268455, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209268829, "dur": 2127, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754551209270961, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209271084, "dur": 361, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209271463, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209271779, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209272051, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209272719, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209273083, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754551209273627, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209273695, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754551209273916, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209274019, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754551209274092, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209274196, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754551209274330, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209274436, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209274495, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ScriptableBuildPipeline.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754551209274557, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209274732, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209274792, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754551209275090, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209275313, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754551209275842, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754551209276294, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754551209276517, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Addressables.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754551209276632, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754551209276685, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209276755, "dur": 3959, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209280792, "dur": 332, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Game.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754551209281130, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209281296, "dur": 97, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Game.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754551209281405, "dur": 207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209281644, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209281843, "dur": 737, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209282611, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209282740, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209282831, "dur": 298, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209283149, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209283227, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209283292, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209283464, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209283532, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209283643, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209283742, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209283808, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754551209283870, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209284102, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754551209284153, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209284216, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209284290, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209284389, "dur": 2494, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209287087, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14485061826818414241.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754551209287656, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754551209287715, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209288061, "dur": 2112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209290185, "dur": 1025, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209291212, "dur": 834, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209292047, "dur": 659, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209293403, "dur": 1654, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.psdimporter@7cfb89931061\\Editor\\SpriteData.cs"}}, {"pid": 12345, "tid": 1, "ts": 1754551209292707, "dur": 2351, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209295441, "dur": 6641, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.aseprite@85ecd97bb34e\\Editor\\Aseprite\\Chunks\\CellChunk.cs"}}, {"pid": 12345, "tid": 1, "ts": 1754551209302229, "dur": 1162, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Shared\\EmptyGraphWindow.cs"}}, {"pid": 12345, "tid": 1, "ts": 1754551209295059, "dur": 8425, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209303484, "dur": 661, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209304146, "dur": 337, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209304484, "dur": 427, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209304918, "dur": 884, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209305803, "dur": 752, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209306556, "dur": 1005, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209307572, "dur": 370, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754551209307944, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209308006, "dur": 3460, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754551209311469, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209311695, "dur": 358, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754551209312054, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209312115, "dur": 2130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754551209314252, "dur": 600, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209314894, "dur": 649, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754551209315545, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209315681, "dur": 995, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754551209316677, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209316890, "dur": 35089, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Profiling.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754551209351981, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209352064, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Profiling.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754551209352139, "dur": 1268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Profiling.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754551209353409, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209353620, "dur": 382, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ResourceManager.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754551209354003, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209354064, "dur": 3086, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ResourceManager.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754551209357151, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209357443, "dur": 344, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754551209357788, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209357852, "dur": 297, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754551209358150, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209358213, "dur": 252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754551209358524, "dur": 1382, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754551209359908, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209360068, "dur": 265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Addressables.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754551209360334, "dur": 196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209360548, "dur": 283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754551209360832, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209360907, "dur": 2222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754551209363131, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209363322, "dur": 4291, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754551209367614, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209367802, "dur": 283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754551209368087, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209368156, "dur": 259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754551209368416, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209368487, "dur": 265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754551209368753, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209368828, "dur": 303, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754551209369133, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209369211, "dur": 11252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754551209380466, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209380663, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209380801, "dur": 451, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754551209381265, "dur": 465, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209381797, "dur": 2527, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754551209384326, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209384463, "dur": 2427, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Addressables.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754551209386892, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209387022, "dur": 27234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754551209414394, "dur": 305, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754551209414730, "dur": 2126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754551209416858, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209416926, "dur": 4280, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754551209421208, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209421300, "dur": 957, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754551209422259, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209422349, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209422434, "dur": 280, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754551209422770, "dur": 321, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754551209423144, "dur": 5260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754551209428407, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209428596, "dur": 572, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ScriptableBuildPipeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754551209429225, "dur": 3197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ScriptableBuildPipeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754551209432495, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209432555, "dur": 9704, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754551209442261, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209442329, "dur": 324, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754551209442681, "dur": 1771, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754551209444454, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209444522, "dur": 35837, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754551209480360, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209480436, "dur": 904, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/SmartVertex.Tools.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754551209481342, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209481477, "dur": 299, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/PsdPlugin.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754551209481992, "dur": 1119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PsdPlugin.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754551209483113, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209483179, "dur": 301, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754551209483515, "dur": 288, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754551209483835, "dur": 306, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754551209484171, "dur": 353, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/RTLTMPro-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754551209484556, "dur": 296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Addressables.DocExampleCode.Editor.Tests.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754551209484883, "dur": 281, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754551209485193, "dur": 309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754551209485537, "dur": 284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754551209485851, "dur": 293, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754551209486175, "dur": 1041, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754551209487217, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209487295, "dur": 842, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Game.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754551209488910, "dur": 152, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551209489963, "dur": 4156844, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Game.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754551213651283, "dur": 238082, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Game.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754551213651249, "dur": 240981, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Game.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754551213896224, "dur": 952, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551213898221, "dur": 235537, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Game.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754551214152594, "dur": 229465, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Game.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754551214152574, "dur": 229501, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Game.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754551214383855, "dur": 90, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754551214382115, "dur": 1851, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Game.dll"}}, {"pid": 12345, "tid": 2, "ts": 1754551209040984, "dur": 212183, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754551209253205, "dur": 324, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754551209253544, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_E9BD9F5D42F6FB25.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754551209254100, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_81F9E86ADEA5E33F.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754551209254609, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754551209254700, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_030E6354148DBFB1.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754551209255568, "dur": 658, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_95F5F54276986F26.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754551209256502, "dur": 496, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754551209257008, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_B407E9C8BAAF60F1.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754551209257191, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_9C727021AC569070.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754551209258266, "dur": 617, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754551209258926, "dur": 330, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754551209259640, "dur": 18821, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754551209278462, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754551209278636, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754551209278716, "dur": 267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754551209278984, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754551209279064, "dur": 25443, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754551209304509, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754551209304698, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754551209304776, "dur": 371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754551209305148, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754551209305222, "dur": 2067, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754551209307291, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754551209307570, "dur": 273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754551209307844, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754551209307907, "dur": 32506, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754551209340415, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754551209340584, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754551209340683, "dur": 311, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754551209340995, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754551209341096, "dur": 2333, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754551209343431, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754551209343611, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754551209343708, "dur": 273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754551209343982, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754551209344061, "dur": 1141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754551209345205, "dur": 383, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754551209345622, "dur": 1411, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 2, "ts": 1754551209347035, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754551209347142, "dur": 569, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754551209349659, "dur": 633312, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 2, "ts": 1754551209989075, "dur": 3845, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.IK.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754551209992922, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754551209993072, "dur": 3591, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754551209996665, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754551209996730, "dur": 3510, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754551210000300, "dur": 3593, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754551210003895, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754551210003986, "dur": 3653, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754551210007642, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754551210007728, "dur": 3479, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754551210011209, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754551210011291, "dur": 3646, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754551210014939, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754551210015007, "dur": 3751, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754551210018760, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754551210018849, "dur": 3612, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754551210022463, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754551210022537, "dur": 3674, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754551210026221, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754551210026298, "dur": 3587, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754551210029888, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754551210029972, "dur": 4080, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Addressables.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754551210034054, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754551210034126, "dur": 3896, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Cinemachine.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754551210038024, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754551210038108, "dur": 4304, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754551210042414, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754551210042480, "dur": 4340, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754551210046883, "dur": 10816, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754551210057701, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754551210057772, "dur": 180, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754551210058000, "dur": 3732, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Psdimporter.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754551210061734, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754551210061837, "dur": 3610, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754551210065449, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754551210065594, "dur": 3733, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Aseprite.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754551210069329, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754551210069561, "dur": 3658, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754551210073220, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754551210073293, "dur": 3601, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754551210076896, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754551210076976, "dur": 3492, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754551210080533, "dur": 3485, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754551210084020, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754551210084123, "dur": 3943, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754551210088067, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754551210088143, "dur": 5708, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754551210093854, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754551210093922, "dur": 172, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754551210094101, "dur": 3986, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754551210098090, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754551210098189, "dur": 4008, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754551210102200, "dur": 331, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754551210102533, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754551210102627, "dur": 3830, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/RTLTMPro-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754551210106459, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754551210106561, "dur": 3853, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ScriptableBuildPipeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754551210110417, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754551210110521, "dur": 4644, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PsdPlugin.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754551210115175, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754551210115260, "dur": 4723, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754551210119985, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754551210120246, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754551210120519, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754551210122765, "dur": 36730, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754551210162908, "dur": 411, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "D:/Unity/Editors/6000.1.0f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 2, "ts": 1754551210163320, "dur": 2344, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "D:/Unity/Editors/6000.1.0f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 2, "ts": 1754551210165665, "dur": 246, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "D:/Unity/Editors/6000.1.0f1/Editor/Data/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 2, "ts": 1754551210159496, "dur": 6427, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754551210165924, "dur": 4218373, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551209041067, "dur": 212203, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551209253285, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_57534491517090DD.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754551209253483, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551209253747, "dur": 411, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551209254699, "dur": 986, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551209256080, "dur": 554, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551209256775, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551209256969, "dur": 1348, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_A250E997A437A30D.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754551209258904, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551209258994, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_BFEF6BF3A445E475.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754551209259481, "dur": 375, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754551209259925, "dur": 12296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754551209272223, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551209272475, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551209272534, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551209273009, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754551209273240, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754551209273371, "dur": 2265, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551209275667, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551209275768, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551209275973, "dur": 2053, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551209278164, "dur": 3872, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551209282048, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551209282180, "dur": 1853, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551209284085, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551209284225, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551209284344, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551209284446, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551209284562, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551209284957, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Addressables.DocExampleCode.Editor.Tests.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754551209285066, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/RTLTMPro-Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754551209285230, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551209285487, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551209285955, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17346584914308636752.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754551209286455, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2157608619508796868.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754551209286580, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551209286662, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10411718816959651794.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754551209286899, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15875538839575725175.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754551209287104, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9703144790800738880.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754551209287721, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551209288727, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3388064732626934676.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754551209289029, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551209290125, "dur": 2602, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.cinemachine@b66fdb7cd1f2\\Editor\\Utility\\CmCameraInspectorUtility.cs"}}, {"pid": 12345, "tid": 3, "ts": 1754551209289501, "dur": 3316, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551209292862, "dur": 2039, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.psdimporter@7cfb89931061\\Editor\\PSDLayerMappingStrategy.cs"}}, {"pid": 12345, "tid": 3, "ts": 1754551209295039, "dur": 4586, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.psdimporter@7cfb89931061\\Editor\\PSDImporterEditor.cs"}}, {"pid": 12345, "tid": 3, "ts": 1754551209292818, "dur": 7358, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551209300177, "dur": 919, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551209301097, "dur": 579, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551209302420, "dur": 2055, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.spriteshape@49c1d1a01890\\Editor\\SpriteShapeAnalytics.cs"}}, {"pid": 12345, "tid": 3, "ts": 1754551209301677, "dur": 3096, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551209304844, "dur": 469, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551209305314, "dur": 859, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551209306174, "dur": 598, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551209306773, "dur": 1327, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551209308102, "dur": 277, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754551209308433, "dur": 46025, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754551209354459, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551209354666, "dur": 293, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754551209354960, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551209355018, "dur": 42331, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754551209397351, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551209397444, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551209397519, "dur": 283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754551209397833, "dur": 1733, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754551209399638, "dur": 333, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754551209400009, "dur": 358, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754551209400408, "dur": 1165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754551209401574, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551209401644, "dur": 1121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754551209402782, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551209402855, "dur": 433, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754551209403328, "dur": 1085, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754551209404493, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551209404743, "dur": 389, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754551209405167, "dur": 5485, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754551209410731, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551209410809, "dur": 273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754551209411125, "dur": 2613, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754551209413740, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551209413814, "dur": 311, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754551209414194, "dur": 4940, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754551209419210, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551209419284, "dur": 296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754551209419642, "dur": 286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754551209419965, "dur": 33131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754551209453169, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551209453237, "dur": 329, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754551209453608, "dur": 6014, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754551209459624, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551209459715, "dur": 3373, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Path.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754551209463090, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551209463199, "dur": 7009, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754551209470211, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551209470285, "dur": 14243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754551209484616, "dur": 317, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754551209484964, "dur": 313, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754551209485308, "dur": 313, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754551209485651, "dur": 293, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754551209485974, "dur": 303, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/SmartVertex.EditorTools.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754551209486307, "dur": 825, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754551209487140, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551209487224, "dur": 1104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754551209488330, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551209488418, "dur": 739, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/SmartVertex.EditorTools.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754551209489215, "dur": 801, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/ScriptablePacker.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754551209490017, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551209490082, "dur": 777, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754551209490860, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551209490922, "dur": 769, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754551209491692, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551209491755, "dur": 821, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754551209492578, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551209492654, "dur": 938, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754551209493593, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551209493660, "dur": 716, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754551209494378, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551209494460, "dur": 1384, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Addressables.DocExampleCode.Editor.Tests.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754551209495846, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551209495952, "dur": 811, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754551209496765, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551209496859, "dur": 847, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/RTLTMPro-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754551209497715, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551209497807, "dur": 871, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754551209498680, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551209498769, "dur": 877, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754551209499647, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551209499723, "dur": 861, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754551209500586, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551209500676, "dur": 923, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754551209501601, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551209501694, "dur": 280, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754551209502014, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754551209502294, "dur": 3157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754551209505454, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551209505532, "dur": 2783, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754551209508440, "dur": 546, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551209508987, "dur": 576, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551209509563, "dur": 529, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551209510093, "dur": 802, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551209510896, "dur": 793, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551209511689, "dur": 932, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551209512622, "dur": 455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551209513202, "dur": 54, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551209517527, "dur": 471558, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551209989087, "dur": 3833, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754551209992922, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551209993061, "dur": 3550, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754551209996614, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551209996708, "dur": 3373, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ScriptableBuildPipeline.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754551210000083, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551210000151, "dur": 3937, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Profiling.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754551210004152, "dur": 4469, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754551210008682, "dur": 3528, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.PixelPerfect.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754551210012212, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551210012285, "dur": 3326, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754551210015674, "dur": 3510, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754551210019245, "dur": 11851, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754551210031098, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551210031185, "dur": 3798, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/SmartVertex.Tools.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754551210034985, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551210035105, "dur": 4337, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754551210039505, "dur": 3629, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InternalAPIEngineBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754551210043194, "dur": 3548, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Common.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754551210046804, "dur": 3714, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754551210050578, "dur": 4792, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754551210055373, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551210055459, "dur": 3771, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InternalAPIEditorBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754551210059232, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551210059316, "dur": 3725, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754551210063043, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551210063163, "dur": 4853, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Addressables.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754551210068018, "dur": 226, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551210068246, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Addressables.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754551210068370, "dur": 4133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Aseprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754551210072505, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551210072596, "dur": 3674, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754551210076341, "dur": 3937, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Cinemachine.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754551210080286, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551210080367, "dur": 3651, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.SpriteShape.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754551210084020, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551210084115, "dur": 3976, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Settings.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754551210088093, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551210088157, "dur": 3728, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754551210091887, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551210091960, "dur": 3529, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754551210095549, "dur": 4056, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/RTLTMPro-Tests.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754551210099607, "dur": 284, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551210099892, "dur": 186, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/RTLTMPro-Tests.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754551210100085, "dur": 6372, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754551210106459, "dur": 1020, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551210107509, "dur": 4478, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754551210111990, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551210112062, "dur": 157, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754551210112228, "dur": 3837, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754551210116067, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551210116156, "dur": 6316, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/ScriptablePacker.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754551210122473, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551210122652, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551210122802, "dur": 43131, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754551210165934, "dur": 4218323, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209041169, "dur": 212317, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209253488, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_F2B74C76ABB96245.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754551209253563, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209254087, "dur": 2113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209256504, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209256779, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209256990, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_2D35B77B8A4D18E6.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754551209258587, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209258688, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209259407, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754551209260481, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209260679, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Unsafe.dll_8C9D4507428A9FC0.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754551209260758, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209260824, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754551209260878, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209260937, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754551209261346, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754551209261521, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209261766, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209261902, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754551209262216, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754551209262341, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_EC1F0C20321316E3.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754551209262406, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209263481, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209263550, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209264078, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4747638433968585886.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754551209264276, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209264484, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209264615, "dur": 2236, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209266905, "dur": 1044, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209267979, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209268154, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209268877, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Runtime.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754551209269314, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754551209269440, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209269801, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754551209269852, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209269923, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754551209270293, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754551209270465, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209270869, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209270923, "dur": 271, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1754551209271203, "dur": 441, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209271676, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209271789, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754551209271844, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209272053, "dur": 2385, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209275402, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209275630, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754551209275740, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209276269, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754551209276368, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754551209276713, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209276773, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209276834, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209276962, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209277037, "dur": 197, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209277291, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209277460, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209277755, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209277883, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209278089, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209278160, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209278840, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209279195, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209279271, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209279361, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209279499, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209279844, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209280065, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209280377, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209280477, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209280587, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209280827, "dur": 277, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209281141, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209281303, "dur": 796, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/ScriptablePacker.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1754551209282106, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209282269, "dur": 205, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209282518, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209282696, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754551209282774, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209282858, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209282932, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209282999, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754551209283068, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209283168, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209283230, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209283297, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209283417, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209283528, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209283586, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209283648, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209283930, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754551209284029, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209284096, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Psdimporter.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754551209284218, "dur": 463, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209285407, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/11294775302662683298.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754551209285520, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9870516708743257357.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754551209286023, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/11344994280883157806.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754551209286504, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17525389461119239690.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754551209286597, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209286669, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15085861467720516389.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754551209287130, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1723042715960067801.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754551209287451, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209287776, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2499735490941455596.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754551209288012, "dur": 1957, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209289988, "dur": 926, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209290915, "dur": 706, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209291622, "dur": 734, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209292689, "dur": 528, "ph": "X", "name": "File", "args": {"detail": "Assets\\Scripts\\Game\\Commands\\DestroyCommand.cs"}}, {"pid": 12345, "tid": 4, "ts": 1754551209292356, "dur": 885, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209293242, "dur": 1021, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209294851, "dur": 2271, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.aseprite@85ecd97bb34e\\Editor\\Aseprite\\Chunks\\TilesetChunk.cs"}}, {"pid": 12345, "tid": 4, "ts": 1754551209294264, "dur": 2943, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209297676, "dur": 908, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.addressables@16eddacf275e\\Tests\\Editor\\DocExampleCode\\ScriptReference\\UsingWebRequestOverride.cs"}}, {"pid": 12345, "tid": 4, "ts": 1754551209297208, "dur": 2929, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209300138, "dur": 894, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209301034, "dur": 718, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209301753, "dur": 785, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209302539, "dur": 318, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209302857, "dur": 336, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209303246, "dur": 31549, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.tilemap.extras@eefc9f6533f8\\Editor\\Tiles\\RuleTile\\RuleTileTemplateUtility.cs"}}, {"pid": 12345, "tid": 4, "ts": 1754551209303194, "dur": 32508, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209335707, "dur": 297, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754551209336006, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209336074, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754551209336170, "dur": 3899, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754551209340071, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209340285, "dur": 295, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754551209340581, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209340644, "dur": 317, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754551209340962, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209341068, "dur": 303, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754551209341372, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209341447, "dur": 1455, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754551209342903, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209343110, "dur": 295, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754551209343406, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209343464, "dur": 2147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754551209345613, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209345800, "dur": 292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754551209346093, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209346154, "dur": 3214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754551209349371, "dur": 197, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209349662, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209349727, "dur": 1091, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754551209350820, "dur": 230, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209351070, "dur": 1164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754551209352236, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209352369, "dur": 290, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ScriptableBuildPipeline.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754551209352661, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209352734, "dur": 821, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ScriptableBuildPipeline.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754551209353557, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209353728, "dur": 303, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754551209354090, "dur": 1365, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754551209355456, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209355634, "dur": 310, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754551209355945, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209356014, "dur": 12351, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754551209368373, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209368568, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754551209368819, "dur": 51443, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754551209420327, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209420391, "dur": 939, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754551209421332, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209421398, "dur": 5597, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754551209426998, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209427239, "dur": 1584, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754551209428825, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209428965, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209429037, "dur": 302, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754551209429444, "dur": 1050, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754551209430578, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209430724, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209431340, "dur": 302, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754551209431695, "dur": 305, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754551209432047, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209432111, "dur": 6427, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754551209438540, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209438616, "dur": 308, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Addressables.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754551209438965, "dur": 308, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754551209439315, "dur": 419, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754551209439769, "dur": 362, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754551209440175, "dur": 321, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754551209440535, "dur": 428, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754551209441004, "dur": 577, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754551209441630, "dur": 307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Path.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754551209441981, "dur": 291, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754551209442311, "dur": 259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754551209442605, "dur": 286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754551209442921, "dur": 16591, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754551209459598, "dur": 3020, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754551209462620, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209462733, "dur": 1481, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754551209464215, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209464300, "dur": 1175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754551209465550, "dur": 2039, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754551209467666, "dur": 2632, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754551209470299, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209470375, "dur": 9475, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Addressables.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754551209479852, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209480008, "dur": 284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/SmartVertex.Tools.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754551209480338, "dur": 283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/RTLTMPro.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754551209480770, "dur": 830, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/RTLTMPro.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754551209481616, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209482037, "dur": 294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Game.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754551209482332, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209482413, "dur": 264, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754551209482714, "dur": 270, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754551209483014, "dur": 285, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754551209483370, "dur": 291, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754551209483695, "dur": 305, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754551209484030, "dur": 299, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754551209484359, "dur": 335, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754551209484737, "dur": 271, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754551209485040, "dur": 309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Psdimporter.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754551209485379, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754551209485658, "dur": 341, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/ScriptablePacker.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754551209486036, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/RTLTMPro-Tests.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754551209486291, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209486356, "dur": 776, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754551209487134, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209487219, "dur": 890, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754551209488112, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209488195, "dur": 871, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/RTLTMPro-Tests.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754551209489068, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209489140, "dur": 1198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754551209490340, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209490416, "dur": 1117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754551209491534, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209491614, "dur": 899, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754551209492515, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209492612, "dur": 883, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Psdimporter.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754551209493496, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209493570, "dur": 753, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754551209494325, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209494412, "dur": 11582, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754551209505996, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209506306, "dur": 624, "ph": "X", "name": "File", "args": {"detail": "D:\\Unity\\Editors\\6000.1.0f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Runtime.Serialization.Xml.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754551209506283, "dur": 1639, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209507924, "dur": 535, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209508459, "dur": 659, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209509119, "dur": 500, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209509620, "dur": 635, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209510256, "dur": 676, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209510933, "dur": 647, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209511582, "dur": 942, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209513167, "dur": 798, "ph": "X", "name": "File", "args": {"detail": "D:\\Unity\\Editors\\6000.1.0f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\api-ms-win-core-rtlsupport-l1-1-0.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754551209514304, "dur": 741, "ph": "X", "name": "File", "args": {"detail": "D:\\Unity\\Editors\\6000.1.0f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\api-ms-win-core-memory-l1-1-0.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754551209515046, "dur": 1278, "ph": "X", "name": "File", "args": {"detail": "D:\\Unity\\Editors\\6000.1.0f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\api-ms-win-core-localization-l1-2-0.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754551209512525, "dur": 4179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209516765, "dur": 58, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209517528, "dur": 471553, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209989084, "dur": 4345, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754551209993431, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551209993493, "dur": 4211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/RTLTMPro.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754551209997764, "dur": 3468, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Animation.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754551210001289, "dur": 3548, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Common.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754551210004893, "dur": 4508, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754551210009403, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551210009466, "dur": 4148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754551210013616, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551210013686, "dur": 3597, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754551210017284, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551210017359, "dur": 3595, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754551210020966, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551210021045, "dur": 3602, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ResourceManager.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754551210024650, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551210024722, "dur": 10269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754551210034993, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551210035118, "dur": 3646, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754551210038765, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551210038832, "dur": 3529, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.SpriteShape.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754551210042363, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551210042440, "dur": 3671, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Splines.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754551210046113, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551210046182, "dur": 3748, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.PixelPerfect.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754551210049933, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551210050032, "dur": 3942, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.IK.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754551210053976, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551210054121, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.IK.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754551210054181, "dur": 3665, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754551210057894, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754551210058002, "dur": 3710, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754551210061714, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551210061806, "dur": 8837, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Addressables.DocExampleCode.Editor.Tests.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754551210070645, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551210070714, "dur": 158, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Addressables.DocExampleCode.Editor.Tests.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754551210070880, "dur": 4889, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754551210075771, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551210075873, "dur": 5646, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754551210081521, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551210081583, "dur": 236, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754551210081826, "dur": 5002, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Splines.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754551210086830, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551210086900, "dur": 227, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Splines.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754551210087135, "dur": 4006, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Common.Path.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754551210091143, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551210091236, "dur": 4089, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Tilemap.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754551210095327, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551210095405, "dur": 3909, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/SmartVertex.EditorTools.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754551210099316, "dur": 306, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551210099648, "dur": 4816, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754551210104466, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551210104575, "dur": 8793, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754551210113369, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551210113451, "dur": 4252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Animation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754551210117705, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551210117768, "dur": 162, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Animation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754551210117942, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551210118007, "dur": 109, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Runtime.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754551210118125, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551210118196, "dur": 95, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mathematics.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754551210118300, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551210118441, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551210118538, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551210118751, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551210118941, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551210119555, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551210119718, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551210120042, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.GPUDriven.Runtime.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754551210120658, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551210121507, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551210122228, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551210122713, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551210122851, "dur": 4029744, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754551214152638, "dur": 317, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Game.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1754551214152597, "dur": 371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Game.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1754551214153058, "dur": 4514, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Game.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1754551214157589, "dur": 226644, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754551214405539, "dur": 8011, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 1012, "tid": 2740, "ts": 1754551214469101, "dur": 14011, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 1012, "tid": 2740, "ts": 1754551214483216, "dur": 7848, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 1012, "tid": 2740, "ts": 1754551214453854, "dur": 39628, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}