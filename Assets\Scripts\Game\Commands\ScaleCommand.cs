using System.Collections;
using UnityEngine;

namespace Game.CommandSystem
{
    /// <summary>
    /// Command to scale a character or object.
    /// </summary>
    public class ScaleCommand : ICoroutineCommand
    {
        /// <summary>Parameters for the scale command.</summary>
        public ScaleParams Parameters { get; }

        /// <summary>Creates a new ScaleCommand.</summary>
        /// <param name="parameters">Scale parameters.</param>
        public ScaleCommand(ScaleParams parameters)
        {
            Parameters = parameters;
        }

        /// <inheritdoc/>
        public IEnumerator Execute()
        {
            // TODO: Implement scaling logic for Parameters.target to Parameters.scale over Parameters.duration
            yield break;
        }
    }

    /// <summary>Parameters for Scale command.</summary>
    [System.Serializable]
    public struct ScaleParams { public string target; public Vector3 scale; public float duration; }
}
