using System;
using System.Collections;

namespace Game.CommandSystem
{
    /// <summary>
    /// Command to play a sound effect.
    /// </summary>
    public class PlaySoundCommand : ICoroutineCommand
    {
        /// <summary>Parameters for the play sound command.</summary>
        public PlaySoundParams Parameters { get; }

        /// <summary>Creates a new PlaySoundCommand.</summary>
        /// <param name="parameters">Play sound parameters.</param>
        public PlaySoundCommand(PlaySoundParams parameters)
        {
            Parameters = parameters;
        }

        /// <inheritdoc/>
        public IEnumerator Execute()
        {
            // TODO: Implement sound playback logic for Parameters.clipName at Parameters.volume
            yield break;
        }
    }

    /// <summary>Parameters for PlaySound command.</summary>
    [Serializable]
    public struct PlaySoundParams { public string clipName; public float volume; }
}
